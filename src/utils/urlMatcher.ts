/**
 * 统一的URL匹配逻辑工具
 * 用于解决标签页检测不一致的问题
 *
 * 基于 Workona 的多层级匹配策略：
 * 1. 精确URL匹配
 * 2. 标准化URL匹配
 * 3. 前缀匹配
 * 4. 域名匹配
 * 5. SPA路由匹配
 * 6. 智能模糊匹配
 */

/**
 * 标准化URL，移除常见的变体差异
 */
export function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    
    // 移除www前缀
    let hostname = urlObj.hostname;
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    // 统一协议为https（用于比较）
    urlObj.protocol = 'https:';
    urlObj.hostname = hostname;
    
    // 移除尾部斜杠
    let pathname = urlObj.pathname;
    if (pathname.endsWith('/') && pathname.length > 1) {
      pathname = pathname.slice(0, -1);
    }
    urlObj.pathname = pathname;
    
    // 移除默认端口
    if ((urlObj.protocol === 'https:' && urlObj.port === '443') ||
        (urlObj.protocol === 'http:' && urlObj.port === '80')) {
      urlObj.port = '';
    }
    
    return urlObj.toString();
  } catch (error) {
    console.warn('URL标准化失败:', url, error);
    return url;
  }
}

/**
 * 提取域名（标准化后）
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    let hostname = urlObj.hostname;
    
    // 移除www前缀
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    return hostname;
  } catch (error) {
    console.warn('域名提取失败:', url, error);
    return '';
  }
}

/**
 * 检查两个URL是否指向同一个网站
 * 使用域名匹配逻辑
 */
export function isSameWebsite(url1: string, url2: string): boolean {
  try {
    const domain1 = extractDomain(url1);
    const domain2 = extractDomain(url2);
    
    if (!domain1 || !domain2) {
      return false;
    }
    
    return domain1 === domain2;
  } catch (error) {
    console.warn('网站匹配检查失败:', url1, url2, error);
    return false;
  }
}

/**
 * 匹配策略枚举
 */
export enum MatchStrategy {
  EXACT = 'exact',           // 精确匹配
  NORMALIZED = 'normalized', // 标准化匹配
  PREFIX = 'prefix',         // 前缀匹配
  DOMAIN = 'domain',         // 域名匹配
  SPA_ROUTE = 'spa_route',   // SPA路由匹配
  FUZZY = 'fuzzy'           // 模糊匹配
}

/**
 * 匹配结果
 */
export interface MatchResult {
  isMatch: boolean;
  strategy: MatchStrategy;
  confidence: number; // 匹配置信度 0-1
  reason?: string;
}

/**
 * 检查标签页URL是否匹配网站URL
 * 基于 Workona 的多层级匹配策略
 */
export function isTabMatchingWebsite(tabUrl: string, websiteUrl: string): boolean {
  const result = getDetailedMatchResult(tabUrl, websiteUrl);
  return result.isMatch;
}

/**
 * 获取详细的匹配结果
 * 按优先级依次尝试不同的匹配策略
 */
export function getDetailedMatchResult(tabUrl: string, websiteUrl: string): MatchResult {
  try {
    // 1. 精确匹配
    if (tabUrl === websiteUrl) {
      console.log('✅ 精确匹配成功:', tabUrl, '<=>', websiteUrl);
      return {
        isMatch: true,
        strategy: MatchStrategy.EXACT,
        confidence: 1.0,
        reason: 'Exact URL match'
      };
    }

    // 2. 标准化URL匹配
    const normalizedTabUrl = normalizeUrl(tabUrl);
    const normalizedWebsiteUrl = normalizeUrl(websiteUrl);

    if (normalizedTabUrl === normalizedWebsiteUrl) {
      console.log('✅ 标准化匹配成功:', tabUrl, '<=>', websiteUrl);
      return {
        isMatch: true,
        strategy: MatchStrategy.NORMALIZED,
        confidence: 0.95,
        reason: 'Normalized URL match'
      };
    }

    // 3. 前缀匹配
    if (normalizedTabUrl.startsWith(normalizedWebsiteUrl)) {
      console.log('✅ 前缀匹配成功:', tabUrl, '<=>', websiteUrl);
      return {
        isMatch: true,
        strategy: MatchStrategy.PREFIX,
        confidence: 0.9,
        reason: 'URL prefix match'
      };
    }

    // 4. SPA路由匹配
    const spaResult = checkSpaRouteMatch(tabUrl, websiteUrl);
    if (spaResult.isMatch) {
      return spaResult;
    }

    // 5. 域名匹配
    const domainResult = checkDomainMatch(tabUrl, websiteUrl);
    if (domainResult.isMatch) {
      return domainResult;
    }

    // 6. 模糊匹配
    const fuzzyResult = checkFuzzyMatch(tabUrl, websiteUrl);
    if (fuzzyResult.isMatch) {
      return fuzzyResult;
    }

    console.log('❌ 所有匹配策略均失败:', tabUrl, '<=>', websiteUrl);
    return {
      isMatch: false,
      strategy: MatchStrategy.EXACT,
      confidence: 0,
      reason: 'No matching strategy succeeded'
    };
  } catch (error) {
    console.warn('标签页网站匹配失败:', tabUrl, websiteUrl, error);
    return {
      isMatch: false,
      strategy: MatchStrategy.EXACT,
      confidence: 0,
      reason: `Error: ${error}`
    };
  }
}

/**
 * SPA路由匹配检查
 * 处理单页应用的路由跳转场景
 */
function checkSpaRouteMatch(tabUrl: string, websiteUrl: string): MatchResult {
  try {
    const tabUrlObj = new URL(tabUrl);
    const websiteUrlObj = new URL(websiteUrl);

    // 域名必须相同
    if (!isSameWebsite(tabUrl, websiteUrl)) {
      return { isMatch: false, strategy: MatchStrategy.SPA_ROUTE, confidence: 0 };
    }

    // 检查是否为SPA路由模式
    const websitePath = websiteUrlObj.pathname;
    const tabPath = tabUrlObj.pathname;

    // 如果网站URL是根路径，标签页URL是子路径，可能是SPA路由
    if ((websitePath === '' || websitePath === '/') && tabPath.length > 1) {
      // 检查是否为常见的SPA路由模式
      const spaPatterns = [
        /^\/dashboard/,
        /^\/app/,
        /^\/admin/,
        /^\/user/,
        /^\/settings/,
        /^\/profile/,
        /^\/workspace/,
        /^\/project/,
        /^\/chat/,
        /^\/conversation/,
      ];

      const isSpaRoute = spaPatterns.some(pattern => pattern.test(tabPath));
      if (isSpaRoute) {
        console.log('✅ SPA路由匹配成功:', tabUrl, '<=>', websiteUrl);
        return {
          isMatch: true,
          strategy: MatchStrategy.SPA_ROUTE,
          confidence: 0.8,
          reason: 'SPA route pattern detected'
        };
      }
    }

    // 检查路径前缀匹配（用于子页面）
    if (websitePath !== '' && websitePath !== '/' && tabPath.startsWith(websitePath)) {
      console.log('✅ SPA子路径匹配成功:', tabUrl, '<=>', websiteUrl);
      return {
        isMatch: true,
        strategy: MatchStrategy.SPA_ROUTE,
        confidence: 0.85,
        reason: 'SPA sub-path match'
      };
    }

    return { isMatch: false, strategy: MatchStrategy.SPA_ROUTE, confidence: 0 };
  } catch (error) {
    return { isMatch: false, strategy: MatchStrategy.SPA_ROUTE, confidence: 0 };
  }
}

/**
 * 域名匹配检查
 * 用于处理同域名下的不同页面
 */
function checkDomainMatch(tabUrl: string, websiteUrl: string): MatchResult {
  try {
    if (!isSameWebsite(tabUrl, websiteUrl)) {
      return { isMatch: false, strategy: MatchStrategy.DOMAIN, confidence: 0 };
    }

    const tabUrlObj = new URL(tabUrl);
    const websiteUrlObj = new URL(websiteUrl);

    // 检查是否为搜索结果页面
    const searchParams = tabUrlObj.searchParams;
    const hasSearchParams = searchParams.has('q') || searchParams.has('search') ||
                           searchParams.has('query') || searchParams.has('s');

    if (hasSearchParams) {
      console.log('✅ 搜索页面域名匹配成功:', tabUrl, '<=>', websiteUrl);
      return {
        isMatch: true,
        strategy: MatchStrategy.DOMAIN,
        confidence: 0.7,
        reason: 'Search page on same domain'
      };
    }

    // 检查是否为动态内容页面
    const dynamicPatterns = [
      /\/\d+/,           // 数字ID
      /\/[a-f0-9-]{36}/, // UUID
      /\/\w{8,}/,        // 长字符串ID
    ];

    const tabPath = tabUrlObj.pathname;
    const isDynamicContent = dynamicPatterns.some(pattern => pattern.test(tabPath));

    if (isDynamicContent) {
      console.log('✅ 动态内容域名匹配成功:', tabUrl, '<=>', websiteUrl);
      return {
        isMatch: true,
        strategy: MatchStrategy.DOMAIN,
        confidence: 0.6,
        reason: 'Dynamic content on same domain'
      };
    }

    return { isMatch: false, strategy: MatchStrategy.DOMAIN, confidence: 0 };
  } catch (error) {
    return { isMatch: false, strategy: MatchStrategy.DOMAIN, confidence: 0 };
  }
}

/**
 * 模糊匹配检查
 * 最后的兜底策略
 */
function checkFuzzyMatch(tabUrl: string, websiteUrl: string): MatchResult {
  try {
    // 只在域名相同的情况下进行模糊匹配
    if (!isSameWebsite(tabUrl, websiteUrl)) {
      return { isMatch: false, strategy: MatchStrategy.FUZZY, confidence: 0 };
    }

    const tabUrlObj = new URL(tabUrl);
    const websiteUrlObj = new URL(websiteUrl);

    // 检查路径相似度
    const tabPath = tabUrlObj.pathname.toLowerCase();
    const websitePath = websiteUrlObj.pathname.toLowerCase();

    // 如果路径包含相同的关键词
    const tabKeywords = tabPath.split('/').filter(p => p.length > 2);
    const websiteKeywords = websitePath.split('/').filter(p => p.length > 2);

    const commonKeywords = tabKeywords.filter(keyword =>
      websiteKeywords.some(wk => wk.includes(keyword) || keyword.includes(wk))
    );

    if (commonKeywords.length > 0) {
      const confidence = Math.min(0.5, commonKeywords.length * 0.2);
      console.log('✅ 模糊匹配成功:', tabUrl, '<=>', websiteUrl, '共同关键词:', commonKeywords);
      return {
        isMatch: true,
        strategy: MatchStrategy.FUZZY,
        confidence,
        reason: `Fuzzy match with common keywords: ${commonKeywords.join(', ')}`
      };
    }

    return { isMatch: false, strategy: MatchStrategy.FUZZY, confidence: 0 };
  } catch (error) {
    return { isMatch: false, strategy: MatchStrategy.FUZZY, confidence: 0 };
  }
}

/**
 * 为chrome.tabs.query()生成URL查询模式
 */
export function generateTabQueryPatterns(websiteUrl: string): string[] {
  try {
    const urlObj = new URL(websiteUrl);
    const patterns: string[] = [];
    
    // 原始URL
    patterns.push(websiteUrl);
    
    // 添加/移除尾部斜杠的版本
    if (websiteUrl.endsWith('/')) {
      patterns.push(websiteUrl.slice(0, -1));
    } else {
      patterns.push(websiteUrl + '/');
    }
    
    // 添加通配符模式用于子路径匹配
    if (!websiteUrl.endsWith('/')) {
      patterns.push(websiteUrl + '/*');
    } else {
      patterns.push(websiteUrl + '*');
    }

    // 添加域名通配符模式（用于SPA路由和动态内容）
    const domain = urlObj.protocol + '//' + urlObj.host;
    patterns.push(domain + '/*');
    
    // www变体
    const hostname = urlObj.hostname;
    if (hostname.startsWith('www.')) {
      // 移除www的版本
      const noWwwUrl = websiteUrl.replace('www.', '');
      patterns.push(noWwwUrl);
      if (!noWwwUrl.endsWith('/')) {
        patterns.push(noWwwUrl + '/');
        patterns.push(noWwwUrl + '/*');
      } else {
        patterns.push(noWwwUrl.slice(0, -1));
        patterns.push(noWwwUrl + '*');
      }
    } else {
      // 添加www的版本
      const wwwUrl = websiteUrl.replace('://', '://www.');
      patterns.push(wwwUrl);
      if (!wwwUrl.endsWith('/')) {
        patterns.push(wwwUrl + '/');
        patterns.push(wwwUrl + '/*');
      } else {
        patterns.push(wwwUrl.slice(0, -1));
        patterns.push(wwwUrl + '*');
      }
    }
    
    // 协议变体
    if (urlObj.protocol === 'https:') {
      const httpUrl = websiteUrl.replace('https://', 'http://');
      patterns.push(httpUrl);
      if (!httpUrl.endsWith('/')) {
        patterns.push(httpUrl + '/');
        patterns.push(httpUrl + '/*');
      } else {
        patterns.push(httpUrl.slice(0, -1));
        patterns.push(httpUrl + '*');
      }
    } else if (urlObj.protocol === 'http:') {
      const httpsUrl = websiteUrl.replace('http://', 'https://');
      patterns.push(httpsUrl);
      if (!httpsUrl.endsWith('/')) {
        patterns.push(httpsUrl + '/');
        patterns.push(httpsUrl + '/*');
      } else {
        patterns.push(httpsUrl.slice(0, -1));
        patterns.push(httpsUrl + '*');
      }
    }
    
    // 去重并返回
    return [...new Set(patterns)];
  } catch (error) {
    console.warn('生成标签页查询模式失败:', websiteUrl, error);
    return [websiteUrl];
  }
}

/**
 * 智能查找匹配的标签页
 * 使用多种策略查找与网站URL匹配的标签页
 */
export async function findMatchingTabs(websiteUrl: string, options: {
  currentWindowOnly?: boolean;
  includeDiscarded?: boolean;
  minConfidence?: number;
} = {}): Promise<{
  tabs: chrome.tabs.Tab[];
  results: Array<{ tab: chrome.tabs.Tab; matchResult: MatchResult }>;
}> {
  const {
    currentWindowOnly = true,
    includeDiscarded = false,
    minConfidence = 0.5
  } = options;

  try {
    // 1. 首先尝试精确查询
    const queryOptions: chrome.tabs.QueryInfo = currentWindowOnly ? { currentWindow: true } : {};
    const patterns = generateTabQueryPatterns(websiteUrl);

    let allTabs: chrome.tabs.Tab[] = [];

    // 尝试使用查询模式
    for (const pattern of patterns) {
      try {
        const tabs = await chrome.tabs.query({ ...queryOptions, url: pattern });
        allTabs.push(...tabs);
      } catch (error) {
        // 某些模式可能不被支持，继续尝试其他模式
        console.warn('查询模式失败:', pattern, error);
      }
    }

    // 2. 如果精确查询没有结果，进行全量匹配
    if (allTabs.length === 0) {
      const allTabsInScope = await chrome.tabs.query(queryOptions);

      const matchResults: Array<{ tab: chrome.tabs.Tab; matchResult: MatchResult }> = [];

      for (const tab of allTabsInScope) {
        if (!tab.url) continue;

        // 跳过已卸载的标签页（除非明确要求包含）
        if (!includeDiscarded && tab.discarded) continue;

        const matchResult = getDetailedMatchResult(tab.url, websiteUrl);
        if (matchResult.isMatch && matchResult.confidence >= minConfidence) {
          matchResults.push({ tab, matchResult });
          allTabs.push(tab);
        }
      }

      // 按置信度排序
      matchResults.sort((a, b) => b.matchResult.confidence - a.matchResult.confidence);

      console.log(`🔍 智能匹配找到 ${allTabs.length} 个标签页，匹配网站: ${websiteUrl}`);

      return {
        tabs: allTabs,
        results: matchResults
      };
    }

    // 3. 对精确查询的结果也进行详细分析
    const results: Array<{ tab: chrome.tabs.Tab; matchResult: MatchResult }> = [];
    for (const tab of allTabs) {
      if (tab.url) {
        const matchResult = getDetailedMatchResult(tab.url, websiteUrl);
        results.push({ tab, matchResult });
      }
    }

    // 去重
    const uniqueTabs = Array.from(new Map(allTabs.map(tab => [tab.id, tab])).values());

    console.log(`🎯 精确查询找到 ${uniqueTabs.length} 个标签页，匹配网站: ${websiteUrl}`);

    return {
      tabs: uniqueTabs,
      results
    };
  } catch (error) {
    console.error('智能查找标签页失败:', error);
    return { tabs: [], results: [] };
  }
}

/**
 * 检查标签页是否可能是同一个网站的不同页面
 * 用于重复标签页检测
 */
export function areTabsSameWebsite(tab1Url: string, tab2Url: string): boolean {
  try {
    const url1 = new URL(tab1Url);
    const url2 = new URL(tab2Url);

    // 域名必须相同
    const domain1 = url1.hostname.replace(/^www\./, '');
    const domain2 = url2.hostname.replace(/^www\./, '');

    return domain1 === domain2;
  } catch (error) {
    return false;
  }
}

/**
 * 调试函数：显示URL匹配信息
 */
export function debugUrlMatch(tabUrl: string, websiteUrl: string): void {
  console.log('🔍 URL匹配调试信息:');
  console.log('  标签页URL:', tabUrl);
  console.log('  网站URL:', websiteUrl);
  console.log('  标准化标签页URL:', normalizeUrl(tabUrl));
  console.log('  标准化网站URL:', normalizeUrl(websiteUrl));
  console.log('  标签页域名:', extractDomain(tabUrl));
  console.log('  网站域名:', extractDomain(websiteUrl));
  console.log('  域名匹配:', isSameWebsite(tabUrl, websiteUrl));

  // 显示详细匹配结果
  const detailedResult = getDetailedMatchResult(tabUrl, websiteUrl);
  console.log('  详细匹配结果:', detailedResult);
}
