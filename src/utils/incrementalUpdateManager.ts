/**
 * 增量更新管理器
 * 基于 Workona 的增量更新策略
 * 只同步变化的部分而不是全量更新，提高性能
 */

import { StorageManager } from './storage';
import { getTabIdentityManager } from './tabIdentity';
import { ERROR_CODES } from './constants';
import type { OperationResult, WorkSpace, TabInfo } from '@/types/workspace';

/**
 * 变更类型枚举
 */
export enum ChangeType {
  ADDED = 'added',
  MODIFIED = 'modified',
  REMOVED = 'removed'
}

/**
 * 变更记录
 */
export interface ChangeRecord {
  id: string;
  type: ChangeType;
  entityType: 'workspace' | 'website' | 'tab' | 'identity';
  entityId: string;
  timestamp: number;
  oldValue?: any;
  newValue?: any;
  metadata?: any;
}

/**
 * 快照数据
 */
interface SnapshotData {
  workspaces: WorkSpace[];
  identities: any[];
  timestamp: number;
  hash: string;
}

/**
 * 增量更新管理器
 * 参考 Workona 的差异检测和增量同步机制
 */
export class IncrementalUpdateManager {
  private static instance: IncrementalUpdateManager;
  private lastSnapshot: SnapshotData | null = null;
  private changeHistory: ChangeRecord[] = [];
  private maxHistorySize = 500;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): IncrementalUpdateManager {
    if (!IncrementalUpdateManager.instance) {
      IncrementalUpdateManager.instance = new IncrementalUpdateManager();
    }
    return IncrementalUpdateManager.instance;
  }

  /**
   * 初始化增量更新管理器
   */
  async initialize(): Promise<OperationResult<void>> {
    try {
      if (this.isInitialized) {
        return { success: true };
      }

      console.log('🔧 初始化增量更新管理器');

      // 创建初始快照
      const snapshotResult = await this.createSnapshot();
      if (!snapshotResult.success) {
        return snapshotResult;
      }

      this.lastSnapshot = snapshotResult.data!;
      this.isInitialized = true;

      console.log('✅ 增量更新管理器初始化完成');
      return { success: true };
    } catch (error) {
      console.error('❌ 初始化增量更新管理器失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.INITIALIZATION_ERROR,
          message: 'Failed to initialize incremental update manager',
          details: error,
        },
      };
    }
  }

  /**
   * 检测并应用增量更新
   */
  async detectAndApplyChanges(): Promise<OperationResult<ChangeRecord[]>> {
    try {
      if (!this.isInitialized) {
        const initResult = await this.initialize();
        if (!initResult.success) {
          return { success: false, error: initResult.error };
        }
      }

      console.log('🔍 检测增量变更');

      // 创建当前快照
      const currentSnapshotResult = await this.createSnapshot();
      if (!currentSnapshotResult.success) {
        return { success: false, error: currentSnapshotResult.error };
      }

      const currentSnapshot = currentSnapshotResult.data!;

      // 如果没有变化，直接返回
      if (this.lastSnapshot && currentSnapshot.hash === this.lastSnapshot.hash) {
        console.log('✅ 无变更检测到');
        return { success: true, data: [] };
      }

      // 检测变更
      const changes = this.detectChanges(this.lastSnapshot, currentSnapshot);
      
      if (changes.length > 0) {
        console.log(`🔄 检测到 ${changes.length} 个变更`);
        
        // 应用变更
        await this.applyChanges(changes);
        
        // 记录变更历史
        this.recordChanges(changes);
        
        // 更新快照
        this.lastSnapshot = currentSnapshot;
      }

      return { success: true, data: changes };
    } catch (error) {
      console.error('❌ 检测和应用增量变更失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.SYNC_ERROR,
          message: 'Failed to detect and apply changes',
          details: error,
        },
      };
    }
  }

  /**
   * 创建当前状态快照
   */
  private async createSnapshot(): Promise<OperationResult<SnapshotData>> {
    try {
      // 获取工作区数据
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      // 获取身份数据
      const identityManager = getTabIdentityManager();
      const identities = Array.from(identityManager.getStats().workspaceBreakdown);

      const snapshot: SnapshotData = {
        workspaces: workspacesResult.data!,
        identities,
        timestamp: Date.now(),
        hash: this.calculateSnapshotHash(workspacesResult.data!, identities)
      };

      return { success: true, data: snapshot };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to create snapshot',
          details: error,
        },
      };
    }
  }

  /**
   * 检测两个快照之间的变更
   */
  private detectChanges(oldSnapshot: SnapshotData | null, newSnapshot: SnapshotData): ChangeRecord[] {
    const changes: ChangeRecord[] = [];

    if (!oldSnapshot) {
      // 初始快照，所有数据都是新增的
      for (const workspace of newSnapshot.workspaces) {
        changes.push(this.createChangeRecord(ChangeType.ADDED, 'workspace', workspace.id, undefined, workspace));
        
        for (const website of workspace.websites) {
          changes.push(this.createChangeRecord(ChangeType.ADDED, 'website', website.id, undefined, website));
        }
      }
      return changes;
    }

    // 检测工作区变更
    this.detectWorkspaceChanges(oldSnapshot.workspaces, newSnapshot.workspaces, changes);

    return changes;
  }

  /**
   * 检测工作区变更
   */
  private detectWorkspaceChanges(
    oldWorkspaces: WorkSpace[],
    newWorkspaces: WorkSpace[],
    changes: ChangeRecord[]
  ): void {
    const oldWorkspaceMap = new Map(oldWorkspaces.map(w => [w.id, w]));
    const newWorkspaceMap = new Map(newWorkspaces.map(w => [w.id, w]));

    // 检测新增和修改的工作区
    for (const newWorkspace of newWorkspaces) {
      const oldWorkspace = oldWorkspaceMap.get(newWorkspace.id);
      
      if (!oldWorkspace) {
        // 新增工作区
        changes.push(this.createChangeRecord(ChangeType.ADDED, 'workspace', newWorkspace.id, undefined, newWorkspace));
        
        // 新增工作区的所有网站
        for (const website of newWorkspace.websites) {
          changes.push(this.createChangeRecord(ChangeType.ADDED, 'website', website.id, undefined, website));
        }
      } else {
        // 检查工作区是否有修改
        if (this.hasWorkspaceChanged(oldWorkspace, newWorkspace)) {
          changes.push(this.createChangeRecord(ChangeType.MODIFIED, 'workspace', newWorkspace.id, oldWorkspace, newWorkspace));
        }

        // 检测网站变更
        this.detectWebsiteChanges(oldWorkspace.websites, newWorkspace.websites, changes);
      }
    }

    // 检测删除的工作区
    for (const oldWorkspace of oldWorkspaces) {
      if (!newWorkspaceMap.has(oldWorkspace.id)) {
        changes.push(this.createChangeRecord(ChangeType.REMOVED, 'workspace', oldWorkspace.id, oldWorkspace, undefined));
      }
    }
  }

  /**
   * 检测网站变更
   */
  private detectWebsiteChanges(
    oldWebsites: any[],
    newWebsites: any[],
    changes: ChangeRecord[]
  ): void {
    const oldWebsiteMap = new Map(oldWebsites.map(w => [w.id, w]));
    const newWebsiteMap = new Map(newWebsites.map(w => [w.id, w]));

    // 检测新增和修改的网站
    for (const newWebsite of newWebsites) {
      const oldWebsite = oldWebsiteMap.get(newWebsite.id);
      
      if (!oldWebsite) {
        changes.push(this.createChangeRecord(ChangeType.ADDED, 'website', newWebsite.id, undefined, newWebsite));
      } else if (this.hasWebsiteChanged(oldWebsite, newWebsite)) {
        changes.push(this.createChangeRecord(ChangeType.MODIFIED, 'website', newWebsite.id, oldWebsite, newWebsite));
      }
    }

    // 检测删除的网站
    for (const oldWebsite of oldWebsites) {
      if (!newWebsiteMap.has(oldWebsite.id)) {
        changes.push(this.createChangeRecord(ChangeType.REMOVED, 'website', oldWebsite.id, oldWebsite, undefined));
      }
    }
  }

  /**
   * 检查工作区是否有变更
   */
  private hasWorkspaceChanged(oldWorkspace: WorkSpace, newWorkspace: WorkSpace): boolean {
    return (
      oldWorkspace.name !== newWorkspace.name ||
      oldWorkspace.isActive !== newWorkspace.isActive ||
      oldWorkspace.order !== newWorkspace.order ||
      oldWorkspace.websites.length !== newWorkspace.websites.length
    );
  }

  /**
   * 检查网站是否有变更
   */
  private hasWebsiteChanged(oldWebsite: any, newWebsite: any): boolean {
    return (
      oldWebsite.title !== newWebsite.title ||
      oldWebsite.url !== newWebsite.url ||
      oldWebsite.favicon !== newWebsite.favicon ||
      oldWebsite.isPinned !== newWebsite.isPinned ||
      oldWebsite.order !== newWebsite.order
    );
  }

  /**
   * 应用变更
   */
  private async applyChanges(changes: ChangeRecord[]): Promise<void> {
    for (const change of changes) {
      try {
        await this.applyChange(change);
        console.log(`✅ 应用变更: ${change.type} ${change.entityType} ${change.entityId}`);
      } catch (error) {
        console.error(`❌ 应用变更失败: ${change.type} ${change.entityType} ${change.entityId}`, error);
      }
    }
  }

  /**
   * 应用单个变更
   */
  private async applyChange(change: ChangeRecord): Promise<void> {
    switch (change.entityType) {
      case 'workspace':
        await this.applyWorkspaceChange(change);
        break;
      case 'website':
        await this.applyWebsiteChange(change);
        break;
      case 'tab':
        await this.applyTabChange(change);
        break;
      case 'identity':
        await this.applyIdentityChange(change);
        break;
    }
  }

  /**
   * 应用工作区变更
   */
  private async applyWorkspaceChange(change: ChangeRecord): Promise<void> {
    // 工作区变更通常已经通过 StorageManager 处理
    // 这里可以添加额外的同步逻辑，如通知其他组件
    console.log(`🔄 工作区变更: ${change.type} ${change.entityId}`);
  }

  /**
   * 应用网站变更
   */
  private async applyWebsiteChange(change: ChangeRecord): Promise<void> {
    // 网站变更可能需要更新相关的标签页身份
    console.log(`🔄 网站变更: ${change.type} ${change.entityId}`);
  }

  /**
   * 应用标签页变更
   */
  private async applyTabChange(change: ChangeRecord): Promise<void> {
    console.log(`🔄 标签页变更: ${change.type} ${change.entityId}`);
  }

  /**
   * 应用身份变更
   */
  private async applyIdentityChange(change: ChangeRecord): Promise<void> {
    console.log(`🔄 身份变更: ${change.type} ${change.entityId}`);
  }

  /**
   * 创建变更记录
   */
  private createChangeRecord(
    type: ChangeType,
    entityType: string,
    entityId: string,
    oldValue: any,
    newValue: any,
    metadata?: any
  ): ChangeRecord {
    return {
      id: this.generateChangeId(),
      type,
      entityType: entityType as any,
      entityId,
      timestamp: Date.now(),
      oldValue,
      newValue,
      metadata
    };
  }

  /**
   * 记录变更历史
   */
  private recordChanges(changes: ChangeRecord[]): void {
    this.changeHistory.unshift(...changes);
    
    // 保持历史记录大小限制
    if (this.changeHistory.length > this.maxHistorySize) {
      this.changeHistory = this.changeHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 计算快照哈希值
   */
  private calculateSnapshotHash(workspaces: WorkSpace[], identities: any[]): string {
    const data = JSON.stringify({ workspaces, identities });
    
    // 简单哈希算法
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return hash.toString(16);
  }

  /**
   * 生成变更ID
   */
  private generateChangeId(): string {
    return `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取变更历史
   */
  getChangeHistory(limit?: number): ChangeRecord[] {
    return limit ? this.changeHistory.slice(0, limit) : [...this.changeHistory];
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    lastSnapshotTime: number | null;
    changeHistorySize: number;
    changesByType: Record<ChangeType, number>;
  } {
    const changesByType = this.changeHistory.reduce((acc, change) => {
      acc[change.type] = (acc[change.type] || 0) + 1;
      return acc;
    }, {} as Record<ChangeType, number>);

    return {
      lastSnapshotTime: this.lastSnapshot?.timestamp || null,
      changeHistorySize: this.changeHistory.length,
      changesByType
    };
  }

  /**
   * 清理过期的变更历史
   */
  cleanupHistory(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    this.changeHistory = this.changeHistory.filter(change => change.timestamp > cutoff);
  }
}

/**
 * 获取增量更新管理器实例
 */
export const getIncrementalUpdateManager = () => IncrementalUpdateManager.getInstance();
