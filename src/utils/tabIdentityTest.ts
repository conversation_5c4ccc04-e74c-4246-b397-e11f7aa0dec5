/**
 * 标签页身份识别系统测试工具
 * 用于验证新的标签识别和匹配策略在各种场景下的准确性
 */

import { getTabIdentityManager } from './tabIdentity';
import { getTabStateSyncManager } from './tabStateSync';
import { findMatchingTabs, getDetailedMatchResult, debugUrlMatch } from './urlMatcher';
import { TabManager } from './tabs';
import { StorageManager } from './storage';

/**
 * 测试场景枚举
 */
export enum TestScenario {
  PAGE_REFRESH = 'page_refresh',
  SPA_NAVIGATION = 'spa_navigation',
  WORKSPACE_SWITCH = 'workspace_switch',
  TAB_MOVE = 'tab_move',
  DUPLICATE_DETECTION = 'duplicate_detection',
  DYNAMIC_URL = 'dynamic_url'
}

/**
 * 测试结果
 */
export interface TestResult {
  scenario: TestScenario;
  success: boolean;
  details: string;
  confidence?: number;
  strategy?: string;
  duration: number;
  timestamp: number;
}

/**
 * 标签页身份识别测试管理器
 */
export class TabIdentityTestManager {
  private static instance: TabIdentityTestManager;
  private testResults: TestResult[] = [];

  private constructor() {}

  static getInstance(): TabIdentityTestManager {
    if (!TabIdentityTestManager.instance) {
      TabIdentityTestManager.instance = new TabIdentityTestManager();
    }
    return TabIdentityTestManager.instance;
  }

  /**
   * 运行所有测试场景
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 开始运行标签页身份识别系统测试');
    
    this.testResults = [];
    
    // 运行各种测试场景
    await this.testPageRefreshScenario();
    await this.testSpaNavigationScenario();
    await this.testWorkspaceSwitchScenario();
    await this.testTabMoveScenario();
    await this.testDuplicateDetectionScenario();
    await this.testDynamicUrlScenario();
    
    // 生成测试报告
    this.generateTestReport();
    
    return this.testResults;
  }

  /**
   * 测试页面刷新场景
   * 验证页面刷新后标签页身份是否能正确识别
   */
  private async testPageRefreshScenario(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔄 测试页面刷新场景');
      
      // 创建测试标签页
      const testUrl = 'https://chatgpt.com/';
      const tab = await chrome.tabs.create({ url: testUrl, active: false });
      
      if (!tab.id) {
        throw new Error('Failed to create test tab');
      }
      
      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 创建身份标识
      const identityManager = getTabIdentityManager();
      const identityResult = await identityManager.createTabIdentity(
        tab.id,
        'test-workspace',
        'test-website',
        testUrl,
        'Test Tab',
        false
      );
      
      if (!identityResult.success) {
        throw new Error('Failed to create tab identity');
      }
      
      const originalIdentity = identityResult.data!;
      
      // 模拟页面刷新（通过重新加载）
      await chrome.tabs.reload(tab.id);
      
      // 等待刷新完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 检查身份是否仍然存在
      const updatedIdentity = identityManager.getIdentityByChromeId(tab.id);
      
      const success = updatedIdentity !== null && 
                     updatedIdentity.internalId === originalIdentity.internalId;
      
      this.testResults.push({
        scenario: TestScenario.PAGE_REFRESH,
        success,
        details: success ? 
          `页面刷新后成功保持身份: ${originalIdentity.internalId}` :
          '页面刷新后身份丢失',
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
      
      // 清理测试标签页
      await chrome.tabs.remove(tab.id);
      
    } catch (error) {
      this.testResults.push({
        scenario: TestScenario.PAGE_REFRESH,
        success: false,
        details: `页面刷新测试失败: ${error}`,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 测试SPA导航场景
   * 验证单页应用路由跳转后的标签页识别
   */
  private async testSpaNavigationScenario(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔀 测试SPA导航场景');
      
      const baseUrl = 'https://chatgpt.com/';
      const spaUrls = [
        'https://chatgpt.com/chat',
        'https://chatgpt.com/chat/new',
        'https://chatgpt.com/settings',
        'https://chatgpt.com/dashboard'
      ];
      
      let successCount = 0;
      const totalTests = spaUrls.length;
      
      for (const spaUrl of spaUrls) {
        const matchResult = getDetailedMatchResult(spaUrl, baseUrl);
        
        if (matchResult.isMatch && matchResult.confidence >= 0.7) {
          successCount++;
          console.log(`✅ SPA路由匹配成功: ${spaUrl} -> ${baseUrl} (${matchResult.strategy}, ${matchResult.confidence})`);
        } else {
          console.log(`❌ SPA路由匹配失败: ${spaUrl} -> ${baseUrl}`);
        }
      }
      
      const success = successCount >= totalTests * 0.8; // 80%成功率
      
      this.testResults.push({
        scenario: TestScenario.SPA_NAVIGATION,
        success,
        details: `SPA导航测试: ${successCount}/${totalTests} 成功`,
        confidence: successCount / totalTests,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.testResults.push({
        scenario: TestScenario.SPA_NAVIGATION,
        success: false,
        details: `SPA导航测试失败: ${error}`,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 测试工作区切换场景
   * 验证工作区切换时标签页的正确识别和移动
   */
  private async testWorkspaceSwitchScenario(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔄 测试工作区切换场景');
      
      // 获取现有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success || workspacesResult.data!.length === 0) {
        throw new Error('No workspaces available for testing');
      }
      
      const workspace = workspacesResult.data![0];
      if (workspace.websites.length === 0) {
        throw new Error('No websites in workspace for testing');
      }
      
      const website = workspace.websites[0];
      
      // 使用智能匹配查找相关标签页
      const matchResult = await TabManager.findMatchingTabsIntelligent(website.url, {
        currentWindowOnly: true,
        minConfidence: 0.6
      });
      
      const success = matchResult.success && matchResult.data!.tabs.length > 0;
      
      this.testResults.push({
        scenario: TestScenario.WORKSPACE_SWITCH,
        success,
        details: success ? 
          `工作区切换测试成功，找到 ${matchResult.data!.tabs.length} 个匹配标签页` :
          '工作区切换测试失败，未找到匹配标签页',
        confidence: matchResult.data!.matchDetails[0]?.confidence,
        strategy: matchResult.data!.matchDetails[0]?.strategy,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.testResults.push({
        scenario: TestScenario.WORKSPACE_SWITCH,
        success: false,
        details: `工作区切换测试失败: ${error}`,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 测试标签页移动场景
   * 验证标签页在窗口间移动时身份的保持
   */
  private async testTabMoveScenario(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('📱 测试标签页移动场景');
      
      // 创建测试标签页
      const testUrl = 'https://example.com/';
      const tab = await chrome.tabs.create({ url: testUrl, active: false });
      
      if (!tab.id) {
        throw new Error('Failed to create test tab');
      }
      
      // 创建身份标识
      const identityManager = getTabIdentityManager();
      const identityResult = await identityManager.createTabIdentity(
        tab.id,
        'test-workspace',
        'test-website',
        testUrl,
        'Test Tab',
        false
      );
      
      if (!identityResult.success) {
        throw new Error('Failed to create tab identity');
      }
      
      const originalIdentity = identityResult.data!;
      
      // 创建新窗口
      const newWindow = await chrome.windows.create({ focused: false });
      if (!newWindow.id) {
        throw new Error('Failed to create new window');
      }
      
      // 移动标签页到新窗口
      await chrome.tabs.move(tab.id, { windowId: newWindow.id, index: -1 });
      
      // 等待移动完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 检查身份是否仍然存在
      const updatedIdentity = identityManager.getIdentityByChromeId(tab.id);
      
      const success = updatedIdentity !== null && 
                     updatedIdentity.internalId === originalIdentity.internalId;
      
      this.testResults.push({
        scenario: TestScenario.TAB_MOVE,
        success,
        details: success ? 
          `标签页移动后成功保持身份: ${originalIdentity.internalId}` :
          '标签页移动后身份丢失',
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
      
      // 清理
      await chrome.windows.remove(newWindow.id);
      
    } catch (error) {
      this.testResults.push({
        scenario: TestScenario.TAB_MOVE,
        success: false,
        details: `标签页移动测试失败: ${error}`,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 测试重复标签页检测场景
   */
  private async testDuplicateDetectionScenario(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔍 测试重复标签页检测场景');
      
      const testUrls = [
        { base: 'https://github.com/', variants: ['https://github.com', 'https://www.github.com/', 'https://github.com/dashboard'] },
        { base: 'https://google.com/', variants: ['https://google.com', 'https://www.google.com/', 'https://google.com/search?q=test'] }
      ];
      
      let successCount = 0;
      let totalTests = 0;
      
      for (const { base, variants } of testUrls) {
        for (const variant of variants) {
          totalTests++;
          const matchResult = getDetailedMatchResult(variant, base);
          
          if (matchResult.isMatch) {
            successCount++;
            console.log(`✅ 重复检测成功: ${variant} -> ${base} (${matchResult.strategy})`);
          } else {
            console.log(`❌ 重复检测失败: ${variant} -> ${base}`);
          }
        }
      }
      
      const success = successCount >= totalTests * 0.8;
      
      this.testResults.push({
        scenario: TestScenario.DUPLICATE_DETECTION,
        success,
        details: `重复标签页检测: ${successCount}/${totalTests} 成功`,
        confidence: successCount / totalTests,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.testResults.push({
        scenario: TestScenario.DUPLICATE_DETECTION,
        success: false,
        details: `重复标签页检测测试失败: ${error}`,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 测试动态URL场景
   */
  private async testDynamicUrlScenario(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🌐 测试动态URL场景');
      
      const dynamicUrls = [
        { base: 'https://github.com/', dynamic: 'https://github.com/user/repo/issues/123' },
        { base: 'https://stackoverflow.com/', dynamic: 'https://stackoverflow.com/questions/12345/some-question' },
        { base: 'https://medium.com/', dynamic: 'https://medium.com/@user/article-title-abc123' }
      ];
      
      let successCount = 0;
      
      for (const { base, dynamic } of dynamicUrls) {
        const matchResult = getDetailedMatchResult(dynamic, base);
        
        if (matchResult.isMatch && matchResult.confidence >= 0.6) {
          successCount++;
          console.log(`✅ 动态URL匹配成功: ${dynamic} -> ${base} (${matchResult.strategy})`);
        } else {
          console.log(`❌ 动态URL匹配失败: ${dynamic} -> ${base}`);
        }
      }
      
      const success = successCount >= dynamicUrls.length * 0.7;
      
      this.testResults.push({
        scenario: TestScenario.DYNAMIC_URL,
        success,
        details: `动态URL测试: ${successCount}/${dynamicUrls.length} 成功`,
        confidence: successCount / dynamicUrls.length,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.testResults.push({
        scenario: TestScenario.DYNAMIC_URL,
        success: false,
        details: `动态URL测试失败: ${error}`,
        duration: Date.now() - startTime,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 生成测试报告
   */
  private generateTestReport(): void {
    console.log('\n📊 标签页身份识别系统测试报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const successfulTests = this.testResults.filter(result => result.success).length;
    const successRate = (successfulTests / totalTests) * 100;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`成功测试: ${successfulTests}`);
    console.log(`成功率: ${successRate.toFixed(1)}%`);
    console.log('');
    
    this.testResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const confidence = result.confidence ? ` (置信度: ${(result.confidence * 100).toFixed(1)}%)` : '';
      const strategy = result.strategy ? ` [${result.strategy}]` : '';
      
      console.log(`${status} ${result.scenario}: ${result.details}${confidence}${strategy}`);
      console.log(`   耗时: ${result.duration}ms`);
    });
    
    console.log('='.repeat(50));
    
    if (successRate >= 80) {
      console.log('🎉 测试通过！标签页身份识别系统工作正常');
    } else {
      console.log('⚠️ 测试未完全通过，需要进一步优化');
    }
  }

  /**
   * 获取测试结果
   */
  getTestResults(): TestResult[] {
    return this.testResults;
  }

  /**
   * 清除测试结果
   */
  clearTestResults(): void {
    this.testResults = [];
  }
}

/**
 * 获取测试管理器实例
 */
export const getTabIdentityTestManager = () => TabIdentityTestManager.getInstance();

/**
 * 快速运行测试的便捷函数
 */
export async function runTabIdentityTests(): Promise<TestResult[]> {
  const testManager = getTabIdentityTestManager();
  return await testManager.runAllTests();
}
