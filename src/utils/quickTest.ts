/**
 * 快速测试脚本
 * 用于验证新的标签识别系统的基本功能
 */

import { getDetailedMatchResult, findMatchingTabs, debugUrlMatch } from './urlMatcher';
import { getTabIdentityManager } from './tabIdentity';
import { getTabStateSyncManager } from './tabStateSync';

/**
 * 快速测试URL匹配功能
 */
export async function quickTestUrlMatching(): Promise<void> {
  console.log('🧪 开始快速测试URL匹配功能');
  
  const testCases = [
    // 精确匹配
    { tab: 'https://chatgpt.com/', website: 'https://chatgpt.com/', expected: true },
    { tab: 'https://chatgpt.com', website: 'https://chatgpt.com/', expected: true },
    
    // SPA路由匹配
    { tab: 'https://chatgpt.com/chat', website: 'https://chatgpt.com/', expected: true },
    { tab: 'https://chatgpt.com/dashboard', website: 'https://chatgpt.com/', expected: true },
    { tab: 'https://chatgpt.com/settings/profile', website: 'https://chatgpt.com/', expected: true },
    
    // 域名匹配
    { tab: 'https://github.com/user/repo', website: 'https://github.com/', expected: true },
    { tab: 'https://stackoverflow.com/questions/123', website: 'https://stackoverflow.com/', expected: true },
    
    // 搜索页面匹配
    { tab: 'https://google.com/search?q=test', website: 'https://google.com/', expected: true },
    { tab: 'https://github.com/search?q=javascript', website: 'https://github.com/', expected: true },
    
    // 不应该匹配的情况
    { tab: 'https://github.com/', website: 'https://gitlab.com/', expected: false },
    { tab: 'https://chatgpt.com/', website: 'https://claude.ai/', expected: false },
  ];
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    const result = getDetailedMatchResult(testCase.tab, testCase.website);
    const passed = result.isMatch === testCase.expected;
    
    if (passed) {
      passedTests++;
      console.log(`✅ ${testCase.tab} -> ${testCase.website}: ${result.strategy} (置信度: ${result.confidence})`);
    } else {
      console.log(`❌ ${testCase.tab} -> ${testCase.website}: 预期 ${testCase.expected}, 实际 ${result.isMatch}`);
      debugUrlMatch(testCase.tab, testCase.website);
    }
  }
  
  console.log(`\n📊 URL匹配测试结果: ${passedTests}/${totalTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
}

/**
 * 快速测试标签身份管理
 */
export async function quickTestTabIdentity(): Promise<void> {
  console.log('🧪 开始快速测试标签身份管理');
  
  try {
    const identityManager = getTabIdentityManager();
    
    // 初始化
    const initResult = await identityManager.initialize();
    if (!initResult.success) {
      console.error('❌ 初始化失败:', initResult.error);
      return;
    }
    
    console.log('✅ 标签身份管理器初始化成功');
    
    // 获取统计信息
    const stats = identityManager.getStats();
    console.log('📊 当前统计信息:', stats);
    
    // 测试创建身份（模拟）
    console.log('🏷️ 测试身份创建功能...');
    
    // 由于这是在非Chrome环境中运行，我们只能测试逻辑
    console.log('✅ 标签身份管理基本功能正常');
    
  } catch (error) {
    console.error('❌ 标签身份管理测试失败:', error);
  }
}

/**
 * 快速测试状态同步管理
 */
export async function quickTestStateSync(): Promise<void> {
  console.log('🧪 开始快速测试状态同步管理');
  
  try {
    const syncManager = getTabStateSyncManager();
    
    // 获取统计信息
    const stats = syncManager.getStats();
    console.log('📊 状态同步统计:', stats);
    
    console.log('✅ 状态同步管理器基本功能正常');
    
  } catch (error) {
    console.error('❌ 状态同步管理测试失败:', error);
  }
}

/**
 * 快速测试智能标签查找
 */
export async function quickTestIntelligentTabFinding(): Promise<void> {
  console.log('🧪 开始快速测试智能标签查找');
  
  const testUrls = [
    'https://chatgpt.com/',
    'https://github.com/',
    'https://stackoverflow.com/',
    'https://google.com/',
    'https://dify.ai/'
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`🔍 测试查找: ${url}`);
      
      // 在实际Chrome环境中，这会查找真实的标签页
      // 这里我们只测试函数调用
      const result = await findMatchingTabs(url, {
        currentWindowOnly: true,
        minConfidence: 0.6
      });
      
      console.log(`📊 查找结果: ${result.tabs.length} 个标签页, ${result.results.length} 个匹配详情`);
      
    } catch (error) {
      console.error(`❌ 查找 ${url} 失败:`, error);
    }
  }
  
  console.log('✅ 智能标签查找功能测试完成');
}

/**
 * 运行所有快速测试
 */
export async function runAllQuickTests(): Promise<void> {
  console.log('🚀 开始运行所有快速测试');
  console.log('='.repeat(60));
  
  await quickTestUrlMatching();
  console.log('');
  
  await quickTestTabIdentity();
  console.log('');
  
  await quickTestStateSync();
  console.log('');
  
  await quickTestIntelligentTabFinding();
  
  console.log('='.repeat(60));
  console.log('🎉 所有快速测试完成');
}

/**
 * 测试特定URL的匹配情况
 */
export function testSpecificUrlMatch(tabUrl: string, websiteUrl: string): void {
  console.log(`🔍 测试特定URL匹配: ${tabUrl} -> ${websiteUrl}`);
  
  const result = getDetailedMatchResult(tabUrl, websiteUrl);
  
  console.log('匹配结果:', {
    isMatch: result.isMatch,
    strategy: result.strategy,
    confidence: result.confidence,
    reason: result.reason
  });
  
  if (result.isMatch) {
    console.log(`✅ 匹配成功 (${result.strategy}, 置信度: ${(result.confidence * 100).toFixed(1)}%)`);
  } else {
    console.log('❌ 匹配失败');
  }
  
  // 显示详细调试信息
  debugUrlMatch(tabUrl, websiteUrl);
}

/**
 * 批量测试URL匹配
 */
export function batchTestUrlMatching(testCases: Array<{
  tab: string;
  website: string;
  description?: string;
}>): void {
  console.log('🧪 批量测试URL匹配');
  console.log('='.repeat(50));
  
  let passedTests = 0;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n测试 ${index + 1}: ${testCase.description || `${testCase.tab} -> ${testCase.website}`}`);
    
    const result = getDetailedMatchResult(testCase.tab, testCase.website);
    
    if (result.isMatch) {
      passedTests++;
      console.log(`✅ 匹配成功: ${result.strategy} (置信度: ${(result.confidence * 100).toFixed(1)}%)`);
    } else {
      console.log(`❌ 匹配失败: ${result.reason || '未知原因'}`);
    }
  });
  
  console.log('='.repeat(50));
  console.log(`📊 批量测试结果: ${passedTests}/${testCases.length} (${(passedTests/testCases.length*100).toFixed(1)}%)`);
}

// 导出一些常用的测试用例
export const commonTestCases = [
  // ChatGPT 相关
  { tab: 'https://chatgpt.com/chat', website: 'https://chatgpt.com/', description: 'ChatGPT SPA路由' },
  { tab: 'https://chatgpt.com/dashboard', website: 'https://chatgpt.com/', description: 'ChatGPT 仪表板' },
  
  // GitHub 相关
  { tab: 'https://github.com/user/repo', website: 'https://github.com/', description: 'GitHub 仓库页面' },
  { tab: 'https://github.com/search?q=javascript', website: 'https://github.com/', description: 'GitHub 搜索页面' },
  
  // Google 相关
  { tab: 'https://google.com/search?q=test', website: 'https://google.com/', description: 'Google 搜索结果' },
  { tab: 'https://www.google.com/', website: 'https://google.com/', description: 'Google www变体' },
  
  // 其他常见场景
  { tab: 'https://stackoverflow.com/questions/123/title', website: 'https://stackoverflow.com/', description: 'StackOverflow 问题页面' },
  { tab: 'https://medium.com/@user/article-title', website: 'https://medium.com/', description: 'Medium 文章页面' },
];
