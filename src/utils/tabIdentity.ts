/**
 * 标签页身份管理系统
 * 基于 Workona 的多重引用系统和双重ID映射机制
 * 解决标签页刷新、导航后的身份识别问题
 */

// 简单的UUID生成函数，避免外部依赖
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
import { StorageManager } from './storage';
import { ERROR_CODES } from './constants';
import type { OperationResult } from '@/types/workspace';

/**
 * 标签页身份信息
 */
export interface TabIdentity {
  /** 内部唯一标识 (格式: "t-{workspaceId}-{uuid}") */
  internalId: string;
  /** Chrome 原生标签页ID */
  chromeId: number;
  /** 所属工作区ID */
  workspaceId: string;
  /** 网站ID */
  websiteId: string;
  /** 标签页URL */
  url: string;
  /** 标签页标题 */
  title: string;
  /** 创建时间戳 */
  createdAt: number;
  /** 最后更新时间戳 */
  updatedAt: number;
  /** 是否为固定标签页 */
  isPinned: boolean;
  /** 标签页状态 */
  status: 'active' | 'background' | 'discarded' | 'hidden';
}

/**
 * 标签页身份映射关系
 */
interface TabIdentityMapping {
  /** 内部ID → Chrome ID 映射 */
  internalToChrome: Map<string, number>;
  /** Chrome ID → 内部ID 映射 */
  chromeToInternal: Map<number, string>;
  /** 标签页身份详细信息 */
  identities: Map<string, TabIdentity>;
}

/**
 * 标签页身份管理器
 * 参考 Workona 的 TabIdentityManager 实现
 */
export class TabIdentityManager {
  private static instance: TabIdentityManager;
  private mapping: TabIdentityMapping;
  private isInitialized = false;

  private constructor() {
    this.mapping = {
      internalToChrome: new Map(),
      chromeToInternal: new Map(),
      identities: new Map(),
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): TabIdentityManager {
    if (!TabIdentityManager.instance) {
      TabIdentityManager.instance = new TabIdentityManager();
    }
    return TabIdentityManager.instance;
  }

  /**
   * 初始化身份管理器
   * 从存储中恢复映射关系
   */
  async initialize(): Promise<OperationResult<void>> {
    try {
      if (this.isInitialized) {
        return { success: true };
      }

      console.log('🔧 初始化标签页身份管理器');

      // 从存储中恢复映射关系
      const result = await StorageManager.get('tabIdentityMapping');
      if (result.success && result.data) {
        const stored = result.data as any;
        
        // 恢复映射关系
        if (stored.internalToChrome) {
          this.mapping.internalToChrome = new Map(Object.entries(stored.internalToChrome));
        }
        if (stored.chromeToInternal) {
          this.mapping.chromeToInternal = new Map(
            Object.entries(stored.chromeToInternal).map(([k, v]) => [parseInt(k), v as string])
          );
        }
        if (stored.identities) {
          this.mapping.identities = new Map(Object.entries(stored.identities));
        }

        console.log(`✅ 恢复了 ${this.mapping.identities.size} 个标签页身份映射`);
      }

      // 清理无效的映射关系
      await this.cleanupInvalidMappings();

      this.isInitialized = true;
      return { success: true };
    } catch (error) {
      console.error('❌ 初始化标签页身份管理器失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to initialize tab identity manager',
          details: error,
        },
      };
    }
  }

  /**
   * 生成内部唯一标识
   * 格式: "t-{workspaceId}-{uuid}"
   */
  private generateInternalId(workspaceId: string): string {
    const uuid = generateUUID();
    return `t-${workspaceId}-${uuid}`;
  }

  /**
   * 为标签页创建身份标识
   * 参考 Workona 的 identifyItems() 方法
   */
  async createTabIdentity(
    chromeId: number,
    workspaceId: string,
    websiteId: string,
    url: string,
    title: string,
    isPinned: boolean = false
  ): Promise<OperationResult<TabIdentity>> {
    try {
      await this.initialize();

      // 检查是否已存在映射
      const existingInternalId = this.mapping.chromeToInternal.get(chromeId);
      if (existingInternalId) {
        const existingIdentity = this.mapping.identities.get(existingInternalId);
        if (existingIdentity) {
          console.log(`🔄 更新现有标签页身份: ${existingInternalId} -> Chrome ID ${chromeId}`);
          
          // 更新现有身份信息
          existingIdentity.chromeId = chromeId;
          existingIdentity.url = url;
          existingIdentity.title = title;
          existingIdentity.isPinned = isPinned;
          existingIdentity.updatedAt = Date.now();
          
          await this.persistMapping();
          return { success: true, data: existingIdentity };
        }
      }

      // 创建新的身份标识
      const internalId = this.generateInternalId(workspaceId);
      const now = Date.now();

      const identity: TabIdentity = {
        internalId,
        chromeId,
        workspaceId,
        websiteId,
        url,
        title,
        createdAt: now,
        updatedAt: now,
        isPinned,
        status: 'active',
      };

      // 建立双向映射
      this.mapping.internalToChrome.set(internalId, chromeId);
      this.mapping.chromeToInternal.set(chromeId, internalId);
      this.mapping.identities.set(internalId, identity);

      // 持久化到存储
      await this.persistMapping();

      console.log(`✅ 创建标签页身份: ${internalId} -> Chrome ID ${chromeId} (${url})`);
      return { success: true, data: identity };
    } catch (error) {
      console.error('❌ 创建标签页身份失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab identity',
          details: error,
        },
      };
    }
  }

  /**
   * 通过 Chrome ID 获取标签页身份
   */
  getIdentityByChromeId(chromeId: number): TabIdentity | null {
    const internalId = this.mapping.chromeToInternal.get(chromeId);
    if (!internalId) return null;
    
    return this.mapping.identities.get(internalId) || null;
  }

  /**
   * 通过内部ID获取标签页身份
   */
  getIdentityByInternalId(internalId: string): TabIdentity | null {
    return this.mapping.identities.get(internalId) || null;
  }

  /**
   * 通过 Chrome ID 获取内部ID
   */
  getInternalIdByChromeId(chromeId: number): string | null {
    return this.mapping.chromeToInternal.get(chromeId) || null;
  }

  /**
   * 通过内部ID获取 Chrome ID
   */
  getChromeIdByInternalId(internalId: string): number | null {
    return this.mapping.internalToChrome.get(internalId) || null;
  }

  /**
   * 更新标签页的 Chrome ID
   * 用于处理标签页刷新、导航后ID变化的情况
   */
  async updateChromeId(oldChromeId: number, newChromeId: number): Promise<OperationResult<void>> {
    try {
      const internalId = this.mapping.chromeToInternal.get(oldChromeId);
      if (!internalId) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Tab identity not found for old Chrome ID',
            details: { oldChromeId },
          },
        };
      }

      // 更新映射关系
      this.mapping.chromeToInternal.delete(oldChromeId);
      this.mapping.chromeToInternal.set(newChromeId, internalId);
      this.mapping.internalToChrome.set(internalId, newChromeId);

      // 更新身份信息
      const identity = this.mapping.identities.get(internalId);
      if (identity) {
        identity.chromeId = newChromeId;
        identity.updatedAt = Date.now();
      }

      await this.persistMapping();

      console.log(`🔄 更新标签页Chrome ID: ${internalId} (${oldChromeId} -> ${newChromeId})`);
      return { success: true };
    } catch (error) {
      console.error('❌ 更新标签页Chrome ID失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to update Chrome ID',
          details: error,
        },
      };
    }
  }

  /**
   * 移除标签页身份
   */
  async removeTabIdentity(chromeId: number): Promise<OperationResult<void>> {
    try {
      const internalId = this.mapping.chromeToInternal.get(chromeId);
      if (!internalId) {
        return { success: true }; // 已经不存在，视为成功
      }

      // 移除所有映射关系
      this.mapping.chromeToInternal.delete(chromeId);
      this.mapping.internalToChrome.delete(internalId);
      this.mapping.identities.delete(internalId);

      await this.persistMapping();

      console.log(`🗑️ 移除标签页身份: ${internalId} (Chrome ID ${chromeId})`);
      return { success: true };
    } catch (error) {
      console.error('❌ 移除标签页身份失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to remove tab identity',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的所有标签页身份
   */
  getWorkspaceTabIdentities(workspaceId: string): TabIdentity[] {
    const identities: TabIdentity[] = [];
    
    for (const identity of this.mapping.identities.values()) {
      if (identity.workspaceId === workspaceId) {
        identities.push(identity);
      }
    }

    return identities;
  }

  /**
   * 清理无效的映射关系
   * 检查 Chrome 标签页是否仍然存在
   */
  private async cleanupInvalidMappings(): Promise<void> {
    try {
      const allTabs = await chrome.tabs.query({});
      const validChromeIds = new Set(allTabs.map(tab => tab.id!));

      const invalidChromeIds: number[] = [];
      
      for (const chromeId of this.mapping.chromeToInternal.keys()) {
        if (!validChromeIds.has(chromeId)) {
          invalidChromeIds.push(chromeId);
        }
      }

      // 移除无效的映射
      for (const chromeId of invalidChromeIds) {
        const internalId = this.mapping.chromeToInternal.get(chromeId);
        if (internalId) {
          this.mapping.chromeToInternal.delete(chromeId);
          this.mapping.internalToChrome.delete(internalId);
          this.mapping.identities.delete(internalId);
        }
      }

      if (invalidChromeIds.length > 0) {
        console.log(`🧹 清理了 ${invalidChromeIds.length} 个无效的标签页身份映射`);
        await this.persistMapping();
      }
    } catch (error) {
      console.warn('清理无效映射关系失败:', error);
    }
  }

  /**
   * 持久化映射关系到存储
   */
  private async persistMapping(): Promise<void> {
    try {
      const data = {
        internalToChrome: Object.fromEntries(this.mapping.internalToChrome),
        chromeToInternal: Object.fromEntries(this.mapping.chromeToInternal),
        identities: Object.fromEntries(this.mapping.identities),
      };

      await StorageManager.set('tabIdentityMapping', data);
    } catch (error) {
      console.error('持久化标签页身份映射失败:', error);
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    totalIdentities: number;
    activeIdentities: number;
    workspaceBreakdown: Record<string, number>;
  } {
    const stats = {
      totalIdentities: this.mapping.identities.size,
      activeIdentities: 0,
      workspaceBreakdown: {} as Record<string, number>,
    };

    for (const identity of this.mapping.identities.values()) {
      if (identity.status === 'active') {
        stats.activeIdentities++;
      }

      stats.workspaceBreakdown[identity.workspaceId] = 
        (stats.workspaceBreakdown[identity.workspaceId] || 0) + 1;
    }

    return stats;
  }
}

/**
 * 获取标签页身份管理器实例
 */
export const getTabIdentityManager = () => TabIdentityManager.getInstance();
