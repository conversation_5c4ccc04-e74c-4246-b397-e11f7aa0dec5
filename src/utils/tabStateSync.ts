/**
 * 标签页状态实时同步机制
 * 基于 Workona 的实时状态监控和同步策略
 * 
 * 核心功能：
 * 1. Chrome 事件监听
 * 2. 状态计算和差异检测
 * 3. 增量更新和冲突解决
 * 4. 会话重新识别
 */

import { getTabIdentityManager, TabIdentity } from './tabIdentity';
import { getDetailedMatchResult, findMatchingTabs } from './urlMatcher';
import { StorageManager } from './storage';
import { ERROR_CODES } from './constants';
import type { OperationResult, WorkSpace } from '@/types/workspace';

/**
 * 标签页状态变化事件类型
 */
export enum TabStateChangeType {
  CREATED = 'created',
  UPDATED = 'updated',
  MOVED = 'moved',
  ACTIVATED = 'activated',
  REMOVED = 'removed',
  REPLACED = 'replaced',
  WINDOW_CHANGED = 'window_changed'
}

/**
 * 标签页状态变化事件
 */
export interface TabStateChangeEvent {
  type: TabStateChangeType;
  tabId: number;
  changeInfo?: any;
  moveInfo?: chrome.tabs.TabMoveInfo;
  windowId?: number;
  timestamp: number;
  processed: boolean;
}

/**
 * 会话状态
 */
export interface SessionState {
  workspaceId: string;
  tabs: TabIdentity[];
  windowId: number;
  lastUpdated: number;
  status: 'empty' | 'restoring' | 'active' | 'identified';
  hash: string; // 用于检测状态变化
}

/**
 * 状态同步配置
 */
interface SyncConfig {
  /** 状态计算间隔（毫秒） */
  stateCalculationInterval: number;
  /** 事件处理延迟（毫秒） */
  eventProcessingDelay: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 是否启用调试日志 */
  enableDebugLogs: boolean;
}

/**
 * 标签页状态同步管理器
 * 参考 Workona 的 continuousStateSync 机制
 */
export class TabStateSyncManager {
  private static instance: TabStateSyncManager;
  private isInitialized = false;
  private isRunning = false;
  private eventQueue: TabStateChangeEvent[] = [];
  private processingTimer: NodeJS.Timeout | null = null;
  private stateCalculationTimer: NodeJS.Timeout | null = null;
  private currentSessions = new Map<string, SessionState>();
  
  private config: SyncConfig = {
    stateCalculationInterval: 1000, // 每秒计算一次状态
    eventProcessingDelay: 100,      // 100ms 延迟处理事件
    maxRetries: 3,
    enableDebugLogs: false
  };

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): TabStateSyncManager {
    if (!TabStateSyncManager.instance) {
      TabStateSyncManager.instance = new TabStateSyncManager();
    }
    return TabStateSyncManager.instance;
  }

  /**
   * 初始化状态同步管理器
   */
  async initialize(config?: Partial<SyncConfig>): Promise<OperationResult<void>> {
    try {
      if (this.isInitialized) {
        return { success: true };
      }

      console.log('🔄 初始化标签页状态同步管理器');

      // 更新配置
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // 初始化标签身份管理器
      const identityManager = getTabIdentityManager();
      await identityManager.initialize();

      // 设置 Chrome 事件监听器
      this.setupChromeEventListeners();

      // 计算初始状态
      await this.calculateCurrentState();

      this.isInitialized = true;
      console.log('✅ 标签页状态同步管理器初始化完成');

      return { success: true };
    } catch (error) {
      console.error('❌ 初始化标签页状态同步管理器失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.INITIALIZATION_ERROR,
          message: 'Failed to initialize tab state sync manager',
          details: error,
        },
      };
    }
  }

  /**
   * 启动实时同步
   */
  async startSync(): Promise<OperationResult<void>> {
    try {
      if (!this.isInitialized) {
        const initResult = await this.initialize();
        if (!initResult.success) {
          return initResult;
        }
      }

      if (this.isRunning) {
        return { success: true };
      }

      console.log('🚀 启动标签页状态实时同步');

      // 启动状态计算循环
      this.startStateCalculationLoop();

      // 启动事件处理循环
      this.startEventProcessingLoop();

      this.isRunning = true;
      return { success: true };
    } catch (error) {
      console.error('❌ 启动状态同步失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.SYNC_ERROR,
          message: 'Failed to start tab state sync',
          details: error,
        },
      };
    }
  }

  /**
   * 停止实时同步
   */
  stopSync(): void {
    console.log('⏹️ 停止标签页状态实时同步');

    this.isRunning = false;

    if (this.stateCalculationTimer) {
      clearInterval(this.stateCalculationTimer);
      this.stateCalculationTimer = null;
    }

    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
      this.processingTimer = null;
    }
  }

  /**
   * 设置 Chrome 事件监听器
   * 参考 Workona 的事件监听机制
   */
  private setupChromeEventListeners(): void {
    // 标签页创建
    chrome.tabs.onCreated.addListener((tab) => {
      this.queueEvent({
        type: TabStateChangeType.CREATED,
        tabId: tab.id!,
        windowId: tab.windowId,
        timestamp: Date.now(),
        processed: false
      });
    });

    // 标签页更新
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.queueEvent({
        type: TabStateChangeType.UPDATED,
        tabId,
        changeInfo,
        windowId: tab.windowId,
        timestamp: Date.now(),
        processed: false
      });
    });

    // 标签页移动
    chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
      this.queueEvent({
        type: TabStateChangeType.MOVED,
        tabId,
        moveInfo,
        windowId: moveInfo.windowId,
        timestamp: Date.now(),
        processed: false
      });
    });

    // 标签页激活
    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.queueEvent({
        type: TabStateChangeType.ACTIVATED,
        tabId: activeInfo.tabId,
        windowId: activeInfo.windowId,
        timestamp: Date.now(),
        processed: false
      });
    });

    // 标签页移除
    chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
      this.queueEvent({
        type: TabStateChangeType.REMOVED,
        tabId,
        windowId: removeInfo.windowId,
        timestamp: Date.now(),
        processed: false
      });
    });

    // 标签页替换（页面导航）
    chrome.tabs.onReplaced.addListener((addedTabId, removedTabId) => {
      this.queueEvent({
        type: TabStateChangeType.REPLACED,
        tabId: addedTabId,
        changeInfo: { replacedTabId: removedTabId },
        timestamp: Date.now(),
        processed: false
      });
    });

    // 窗口焦点变化
    chrome.windows.onFocusChanged.addListener((windowId) => {
      if (windowId !== chrome.windows.WINDOW_ID_NONE) {
        this.queueEvent({
          type: TabStateChangeType.WINDOW_CHANGED,
          tabId: -1, // 窗口事件没有特定标签页
          windowId,
          timestamp: Date.now(),
          processed: false
        });
      }
    });

    console.log('📡 Chrome 事件监听器设置完成');
  }

  /**
   * 将事件加入队列
   */
  private queueEvent(event: TabStateChangeEvent): void {
    this.eventQueue.push(event);
    
    if (this.config.enableDebugLogs) {
      console.log('📥 事件入队:', event.type, 'Tab ID:', event.tabId);
    }

    // 如果队列过长，移除旧事件
    if (this.eventQueue.length > 100) {
      this.eventQueue = this.eventQueue.slice(-50);
    }
  }

  /**
   * 启动状态计算循环
   * 参考 Workona 的 continuousStateSync
   */
  private startStateCalculationLoop(): void {
    this.stateCalculationTimer = setInterval(async () => {
      try {
        await this.calculateCurrentState();
      } catch (error) {
        console.error('状态计算循环出错:', error);
      }
    }, this.config.stateCalculationInterval);
  }

  /**
   * 启动事件处理循环
   */
  private startEventProcessingLoop(): void {
    const processEvents = async () => {
      if (this.eventQueue.length > 0) {
        await this.processEventQueue();
      }

      if (this.isRunning) {
        this.processingTimer = setTimeout(processEvents, this.config.eventProcessingDelay);
      }
    };

    processEvents();
  }

  /**
   * 计算当前状态
   * 参考 Workona 的 calcOpenWorkspaces
   */
  private async calculateCurrentState(): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return;

      const workspaces = workspacesResult.data!;
      const identityManager = getTabIdentityManager();

      for (const workspace of workspaces) {
        const sessionState = await this.calculateWorkspaceSession(workspace);
        
        // 检查状态是否发生变化
        const currentSession = this.currentSessions.get(workspace.id);
        if (!currentSession || currentSession.hash !== sessionState.hash) {
          this.currentSessions.set(workspace.id, sessionState);
          
          // 触发会话重新识别
          await this.reidentifySession(workspace.id, sessionState);
        }
      }
    } catch (error) {
      console.error('计算当前状态失败:', error);
    }
  }

  /**
   * 计算工作区会话状态
   */
  private async calculateWorkspaceSession(workspace: WorkSpace): Promise<SessionState> {
    const identityManager = getTabIdentityManager();
    const workspaceTabs = identityManager.getWorkspaceTabIdentities(workspace.id);
    
    // 计算状态哈希
    const hash = this.calculateStateHash(workspaceTabs);
    
    return {
      workspaceId: workspace.id,
      tabs: workspaceTabs,
      windowId: -1, // 需要根据实际情况确定
      lastUpdated: Date.now(),
      status: workspaceTabs.length === 0 ? 'empty' : 'active',
      hash
    };
  }

  /**
   * 计算状态哈希值
   */
  private calculateStateHash(tabs: TabIdentity[]): string {
    const data = tabs.map(tab => `${tab.internalId}:${tab.url}:${tab.updatedAt}`).join('|');
    return btoa(data).substring(0, 16); // 简单哈希
  }

  /**
   * 会话重新识别
   * 参考 Workona 的 reidentifySession
   */
  private async reidentifySession(workspaceId: string, sessionState: SessionState): Promise<void> {
    try {
      if (this.config.enableDebugLogs) {
        console.log(`🔄 重新识别工作区 ${workspaceId} 的会话状态`);
      }

      // 这里可以添加更复杂的会话识别逻辑
      // 例如检测标签页的URL变化、标题变化等

      // 保存会话状态
      await this.saveSessionState(workspaceId, sessionState);
    } catch (error) {
      console.error('会话重新识别失败:', error);
    }
  }

  /**
   * 处理事件队列
   */
  private async processEventQueue(): Promise<void> {
    const eventsToProcess = this.eventQueue.filter(event => !event.processed);
    
    for (const event of eventsToProcess) {
      try {
        await this.processEvent(event);
        event.processed = true;
      } catch (error) {
        console.error('处理事件失败:', event, error);
      }
    }

    // 清理已处理的事件
    this.eventQueue = this.eventQueue.filter(event => !event.processed);
  }

  /**
   * 处理单个事件
   */
  private async processEvent(event: TabStateChangeEvent): Promise<void> {
    const identityManager = getTabIdentityManager();

    switch (event.type) {
      case TabStateChangeType.CREATED:
        await this.handleTabCreated(event.tabId, event.windowId!);
        break;

      case TabStateChangeType.UPDATED:
        await this.handleTabUpdated(event.tabId, event.changeInfo);
        break;

      case TabStateChangeType.MOVED:
        await this.handleTabMoved(event.tabId, event.moveInfo!);
        break;

      case TabStateChangeType.REMOVED:
        await identityManager.removeTabIdentity(event.tabId);
        break;

      case TabStateChangeType.REPLACED:
        await this.handleTabReplaced(event.tabId, event.changeInfo.replacedTabId);
        break;

      default:
        // 其他事件类型的处理
        break;
    }
  }

  /**
   * 处理标签页创建事件
   */
  private async handleTabCreated(tabId: number, windowId: number): Promise<void> {
    try {
      const tab = await chrome.tabs.get(tabId);
      if (!tab.url) return;

      // 尝试识别标签页属于哪个工作区
      const workspaceResult = await this.identifyTabWorkspace(tab.url);
      if (workspaceResult.success && workspaceResult.data) {
        const { workspaceId, websiteId } = workspaceResult.data;
        
        const identityManager = getTabIdentityManager();
        await identityManager.createTabIdentity(
          tabId,
          workspaceId,
          websiteId,
          tab.url,
          tab.title || '',
          tab.pinned || false
        );
      }
    } catch (error) {
      console.error('处理标签页创建事件失败:', error);
    }
  }

  /**
   * 处理标签页更新事件
   */
  private async handleTabUpdated(tabId: number, changeInfo: any): Promise<void> {
    if (changeInfo.url) {
      // URL 发生变化，可能需要重新识别标签页
      const identityManager = getTabIdentityManager();
      const identity = identityManager.getIdentityByChromeId(tabId);
      
      if (identity) {
        // 更新现有身份的URL
        identity.url = changeInfo.url;
        identity.updatedAt = Date.now();
        
        if (changeInfo.title) {
          identity.title = changeInfo.title;
        }
      }
    }
  }

  /**
   * 处理标签页移动事件
   */
  private async handleTabMoved(tabId: number, moveInfo: chrome.tabs.TabMoveInfo): Promise<void> {
    const identityManager = getTabIdentityManager();
    const identity = identityManager.getIdentityByChromeId(tabId);
    
    if (identity) {
      identity.updatedAt = Date.now();
    }
  }

  /**
   * 处理标签页替换事件（页面导航）
   */
  private async handleTabReplaced(newTabId: number, oldTabId: number): Promise<void> {
    const identityManager = getTabIdentityManager();
    
    // 更新标签页的 Chrome ID
    await identityManager.updateChromeId(oldTabId, newTabId);
  }

  /**
   * 识别标签页属于哪个工作区
   */
  private async identifyTabWorkspace(url: string): Promise<OperationResult<{
    workspaceId: string;
    websiteId: string;
  }>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      
      for (const workspace of workspaces) {
        for (const website of workspace.websites) {
          const matchResult = getDetailedMatchResult(url, website.url);
          if (matchResult.isMatch && matchResult.confidence >= 0.7) {
            return {
              success: true,
              data: {
                workspaceId: workspace.id,
                websiteId: website.id
              }
            };
          }
        }
      }

      return { success: false, error: { code: ERROR_CODES.NOT_FOUND, message: 'No matching workspace found' } };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to identify tab workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 保存会话状态
   */
  private async saveSessionState(workspaceId: string, sessionState: SessionState): Promise<void> {
    try {
      await StorageManager.set(`sessionState_${workspaceId}`, sessionState);
    } catch (error) {
      console.error('保存会话状态失败:', error);
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    isRunning: boolean;
    eventQueueLength: number;
    activeSessions: number;
    lastStateCalculation: number;
  } {
    return {
      isRunning: this.isRunning,
      eventQueueLength: this.eventQueue.length,
      activeSessions: this.currentSessions.size,
      lastStateCalculation: Date.now()
    };
  }
}

/**
 * 获取标签页状态同步管理器实例
 */
export const getTabStateSyncManager = () => TabStateSyncManager.getInstance();
