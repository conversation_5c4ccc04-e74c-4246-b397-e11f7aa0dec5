/**
 * 事务式操作管理器
 * 基于 Workona 的数据一致性保证机制
 * 确保工作区操作的原子性，支持回滚机制
 */

import { getTabIdentityManager } from './tabIdentity';
import { StorageManager } from './storage';
import { ERROR_CODES } from './constants';
import type { OperationResult, WorkSpace, TabInfo } from '@/types/workspace';

/**
 * 操作类型枚举
 */
export enum OperationType {
  TAB_MOVE = 'tab_move',
  TAB_CREATE_IDENTITY = 'tab_create_identity',
  TAB_REMOVE_IDENTITY = 'tab_remove_identity',
  WORKSPACE_UPDATE = 'workspace_update',
  STORAGE_UPDATE = 'storage_update'
}

/**
 * 事务操作记录
 */
export interface TransactionOperation {
  id: string;
  type: OperationType;
  timestamp: number;
  data: any;
  rollbackData?: any;
}

/**
 * 事务状态
 */
export enum TransactionStatus {
  PENDING = 'pending',
  COMMITTED = 'committed',
  ROLLED_BACK = 'rolled_back',
  FAILED = 'failed'
}

/**
 * 事务记录
 */
export interface Transaction {
  id: string;
  status: TransactionStatus;
  operations: TransactionOperation[];
  startTime: number;
  endTime?: number;
  error?: any;
}

/**
 * 事务式操作管理器
 * 参考 Workona 的原子性操作保证
 */
export class TransactionManager {
  private static instance: TransactionManager;
  private activeTransactions = new Map<string, Transaction>();
  private transactionHistory: Transaction[] = [];
  private maxHistorySize = 100;

  private constructor() {}

  static getInstance(): TransactionManager {
    if (!TransactionManager.instance) {
      TransactionManager.instance = new TransactionManager();
    }
    return TransactionManager.instance;
  }

  /**
   * 开始新事务
   */
  async beginTransaction(): Promise<string> {
    const transactionId = this.generateTransactionId();
    const transaction: Transaction = {
      id: transactionId,
      status: TransactionStatus.PENDING,
      operations: [],
      startTime: Date.now()
    };

    this.activeTransactions.set(transactionId, transaction);
    console.log(`🔄 开始事务: ${transactionId}`);
    
    return transactionId;
  }

  /**
   * 添加操作到事务
   */
  addOperation(
    transactionId: string,
    type: OperationType,
    data: any,
    rollbackData?: any
  ): void {
    const transaction = this.activeTransactions.get(transactionId);
    if (!transaction) {
      throw new Error(`Transaction ${transactionId} not found`);
    }

    const operation: TransactionOperation = {
      id: this.generateOperationId(),
      type,
      timestamp: Date.now(),
      data,
      rollbackData
    };

    transaction.operations.push(operation);
    console.log(`📝 添加操作到事务 ${transactionId}: ${type}`);
  }

  /**
   * 提交事务
   */
  async commitTransaction(transactionId: string): Promise<OperationResult<void>> {
    const transaction = this.activeTransactions.get(transactionId);
    if (!transaction) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Transaction not found',
          details: { transactionId }
        }
      };
    }

    try {
      console.log(`✅ 提交事务 ${transactionId} (${transaction.operations.length} 个操作)`);
      
      // 执行所有操作
      for (const operation of transaction.operations) {
        await this.executeOperation(operation);
      }

      // 标记事务为已提交
      transaction.status = TransactionStatus.COMMITTED;
      transaction.endTime = Date.now();

      // 移动到历史记录
      this.moveToHistory(transaction);
      this.activeTransactions.delete(transactionId);

      console.log(`✅ 事务 ${transactionId} 提交成功`);
      return { success: true };
    } catch (error) {
      console.error(`❌ 事务 ${transactionId} 提交失败:`, error);
      
      // 尝试回滚
      const rollbackResult = await this.rollbackTransaction(transactionId);
      if (!rollbackResult.success) {
        console.error(`❌ 事务 ${transactionId} 回滚也失败:`, rollbackResult.error);
      }

      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Transaction commit failed',
          details: error
        }
      };
    }
  }

  /**
   * 回滚事务
   */
  async rollbackTransaction(transactionId: string): Promise<OperationResult<void>> {
    const transaction = this.activeTransactions.get(transactionId);
    if (!transaction) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Transaction not found',
          details: { transactionId }
        }
      };
    }

    try {
      console.log(`🔄 回滚事务 ${transactionId} (${transaction.operations.length} 个操作)`);
      
      // 按相反顺序执行回滚操作
      const reversedOperations = [...transaction.operations].reverse();
      for (const operation of reversedOperations) {
        if (operation.rollbackData) {
          await this.executeRollbackOperation(operation);
        }
      }

      // 标记事务为已回滚
      transaction.status = TransactionStatus.ROLLED_BACK;
      transaction.endTime = Date.now();

      // 移动到历史记录
      this.moveToHistory(transaction);
      this.activeTransactions.delete(transactionId);

      console.log(`✅ 事务 ${transactionId} 回滚成功`);
      return { success: true };
    } catch (error) {
      console.error(`❌ 事务 ${transactionId} 回滚失败:`, error);
      
      transaction.status = TransactionStatus.FAILED;
      transaction.error = error;
      transaction.endTime = Date.now();

      this.moveToHistory(transaction);
      this.activeTransactions.delete(transactionId);

      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Transaction rollback failed',
          details: error
        }
      };
    }
  }

  /**
   * 执行操作
   */
  private async executeOperation(operation: TransactionOperation): Promise<void> {
    switch (operation.type) {
      case OperationType.TAB_MOVE:
        await this.executeTabMoveOperation(operation);
        break;
      case OperationType.TAB_CREATE_IDENTITY:
        await this.executeTabCreateIdentityOperation(operation);
        break;
      case OperationType.TAB_REMOVE_IDENTITY:
        await this.executeTabRemoveIdentityOperation(operation);
        break;
      case OperationType.WORKSPACE_UPDATE:
        await this.executeWorkspaceUpdateOperation(operation);
        break;
      case OperationType.STORAGE_UPDATE:
        await this.executeStorageUpdateOperation(operation);
        break;
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  /**
   * 执行回滚操作
   */
  private async executeRollbackOperation(operation: TransactionOperation): Promise<void> {
    switch (operation.type) {
      case OperationType.TAB_MOVE:
        await this.rollbackTabMoveOperation(operation);
        break;
      case OperationType.TAB_CREATE_IDENTITY:
        await this.rollbackTabCreateIdentityOperation(operation);
        break;
      case OperationType.TAB_REMOVE_IDENTITY:
        await this.rollbackTabRemoveIdentityOperation(operation);
        break;
      case OperationType.WORKSPACE_UPDATE:
        await this.rollbackWorkspaceUpdateOperation(operation);
        break;
      case OperationType.STORAGE_UPDATE:
        await this.rollbackStorageUpdateOperation(operation);
        break;
      default:
        console.warn(`No rollback handler for operation type: ${operation.type}`);
    }
  }

  /**
   * 执行标签页移动操作
   */
  private async executeTabMoveOperation(operation: TransactionOperation): Promise<void> {
    const { tabIds, targetWindowId } = operation.data;
    await chrome.tabs.move(tabIds, { windowId: targetWindowId, index: -1 });
  }

  /**
   * 回滚标签页移动操作
   */
  private async rollbackTabMoveOperation(operation: TransactionOperation): Promise<void> {
    const { tabIds, originalWindowId } = operation.rollbackData;
    await chrome.tabs.move(tabIds, { windowId: originalWindowId, index: -1 });
  }

  /**
   * 执行标签页身份创建操作
   */
  private async executeTabCreateIdentityOperation(operation: TransactionOperation): Promise<void> {
    const { tabId, workspaceId, websiteId, url, title, isPinned } = operation.data;
    const identityManager = getTabIdentityManager();
    await identityManager.createTabIdentity(tabId, workspaceId, websiteId, url, title, isPinned);
  }

  /**
   * 回滚标签页身份创建操作
   */
  private async rollbackTabCreateIdentityOperation(operation: TransactionOperation): Promise<void> {
    const { tabId } = operation.data;
    const identityManager = getTabIdentityManager();
    await identityManager.removeTabIdentity(tabId);
  }

  /**
   * 执行标签页身份移除操作
   */
  private async executeTabRemoveIdentityOperation(operation: TransactionOperation): Promise<void> {
    const { tabId } = operation.data;
    const identityManager = getTabIdentityManager();
    await identityManager.removeTabIdentity(tabId);
  }

  /**
   * 回滚标签页身份移除操作
   */
  private async rollbackTabRemoveIdentityOperation(operation: TransactionOperation): Promise<void> {
    const { tabId, workspaceId, websiteId, url, title, isPinned } = operation.rollbackData;
    const identityManager = getTabIdentityManager();
    await identityManager.createTabIdentity(tabId, workspaceId, websiteId, url, title, isPinned);
  }

  /**
   * 执行工作区更新操作
   */
  private async executeWorkspaceUpdateOperation(operation: TransactionOperation): Promise<void> {
    const { workspaces } = operation.data;
    await StorageManager.saveWorkspaces(workspaces);
  }

  /**
   * 回滚工作区更新操作
   */
  private async rollbackWorkspaceUpdateOperation(operation: TransactionOperation): Promise<void> {
    const { originalWorkspaces } = operation.rollbackData;
    await StorageManager.saveWorkspaces(originalWorkspaces);
  }

  /**
   * 执行存储更新操作
   */
  private async executeStorageUpdateOperation(operation: TransactionOperation): Promise<void> {
    const { key, value } = operation.data;
    await chrome.storage.local.set({ [key]: value });
  }

  /**
   * 回滚存储更新操作
   */
  private async rollbackStorageUpdateOperation(operation: TransactionOperation): Promise<void> {
    const { key, originalValue } = operation.rollbackData;
    if (originalValue !== undefined) {
      await chrome.storage.local.set({ [key]: originalValue });
    } else {
      await chrome.storage.local.remove(key);
    }
  }

  /**
   * 生成事务ID
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成操作ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 移动事务到历史记录
   */
  private moveToHistory(transaction: Transaction): void {
    this.transactionHistory.unshift(transaction);
    
    // 保持历史记录大小限制
    if (this.transactionHistory.length > this.maxHistorySize) {
      this.transactionHistory = this.transactionHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 获取事务统计信息
   */
  getStats(): {
    activeTransactions: number;
    historySize: number;
    successRate: number;
  } {
    const totalTransactions = this.transactionHistory.length;
    const successfulTransactions = this.transactionHistory.filter(
      tx => tx.status === TransactionStatus.COMMITTED
    ).length;

    return {
      activeTransactions: this.activeTransactions.size,
      historySize: totalTransactions,
      successRate: totalTransactions > 0 ? successfulTransactions / totalTransactions : 0
    };
  }

  /**
   * 清理过期的活跃事务
   */
  cleanupExpiredTransactions(): void {
    const now = Date.now();
    const timeout = 5 * 60 * 1000; // 5分钟超时

    for (const [id, transaction] of this.activeTransactions.entries()) {
      if (now - transaction.startTime > timeout) {
        console.warn(`⚠️ 清理超时事务: ${id}`);
        transaction.status = TransactionStatus.FAILED;
        transaction.error = 'Transaction timeout';
        transaction.endTime = now;
        
        this.moveToHistory(transaction);
        this.activeTransactions.delete(id);
      }
    }
  }
}

/**
 * 获取事务管理器实例
 */
export const getTransactionManager = () => TransactionManager.getInstance();
