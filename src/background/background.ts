import { StorageManager } from '../utils/storage';
import { WorkspaceSwitcher } from '../utils/workspaceSwitcher';
import { COMMANDS } from '../utils/constants';

/**
 * Chrome扩展后台脚本
 */
class BackgroundService {
  constructor() {
    this.init();
  }

  /**
   * 初始化后台服务
   */
  private async init(): Promise<void> {
    // 设置侧边栏行为
    await this.setupSidePanel();

    // 初始化标签页身份识别系统
    try {
      const initResult = await WorkspaceSwitcher.initializeTabIdentitySystem();
      if (initResult.success) {
        console.log('✅ 标签页身份识别系统初始化成功');
      } else {
        console.error('❌ 标签页身份识别系统初始化失败:', initResult.error);
      }
    } catch (error) {
      console.error('❌ 初始化标签页身份识别系统时出错:', error);
    }

    // 监听命令
    this.setupCommandListeners();

    // 监听标签页事件
    this.setupTabListeners();

    // 监听存储变化
    this.setupStorageListeners();

    // 初始化默认数据
    await this.initializeDefaultData();

    console.log('WorkSpace Pro background service initialized');
  }

  /**
   * 设置侧边栏
   */
  private async setupSidePanel(): Promise<void> {
    try {
      // 设置侧边栏在点击扩展图标时打开
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error('Failed to setup side panel:', error);
    }
  }

  /**
   * 设置命令监听器
   */
  private setupCommandListeners(): void {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log('Command received:', command);
      
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          default:
            console.log('Unknown command:', command);
        }
      } catch (error) {
        console.error('Error handling command:', command, error);
      }
    });
  }

  /**
   * 设置标签页监听器（升级版：使用TabStateSyncManager统一管理）
   */
  private setupTabListeners(): void {
    // 注意：TabStateSyncManager 已经在 init() 方法中启动
    // 这里只保留一些特定的业务逻辑监听器

    // 监听标签页激活 - 用于工作区自动切换
    chrome.tabs.onActivated.addListener(async (_activeInfo) => {
      try {
        // 使用智能检测当前应该激活的工作区
        const detectedResult = await WorkspaceSwitcher.detectActiveWorkspaceIntelligent();
        if (detectedResult.success && detectedResult.data) {
          const currentResult = await WorkspaceSwitcher.getCurrentWorkspace();
          if (currentResult.success &&
              (!currentResult.data || currentResult.data.id !== detectedResult.data.id)) {
            // 自动切换到检测到的工作区（不关闭其他标签页）
            await WorkspaceSwitcher.switchToWorkspace(detectedResult.data.id, {
              closeOtherTabs: false,
              focusFirstTab: false,
            });
          }
        }
      } catch (error) {
        console.error('Error handling tab activation:', error);
      }
    });

    // 保留前端状态通知的监听器
    // 这些是业务逻辑，不是底层的状态同步
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        try {
          console.log('Tab updated for UI notification:', tab.url);
          // 通知前端更新状态
          await this.notifyGlobalUserTabsStateChange('tab_updated');
        } catch (error) {
          console.error('Error handling tab update notification:', error);
        }
      }
    });

    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log('Tab created for UI notification:', tab.url);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_created');
      } catch (error) {
        console.error('Error handling tab creation notification:', error);
      }
    });

    chrome.tabs.onRemoved.addListener(async (tabId, removeInfo) => {
      try {
        console.log('Tab removed for UI notification:', tabId);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_removed');
      } catch (error) {
        console.error('Error handling tab removal notification:', error);
      }
    });

    // 监听标签页移动
    chrome.tabs.onMoved.addListener(async (tabId, moveInfo) => {
      try {
        console.log('Tab moved:', tabId, moveInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_moved');
      } catch (error) {
        console.error('Error handling tab move:', error);
      }
    });

    // 监听标签页附加到窗口
    chrome.tabs.onAttached.addListener(async (tabId, attachInfo) => {
      try {
        console.log('Tab attached:', tabId, attachInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_attached');
      } catch (error) {
        console.error('Error handling tab attach:', error);
      }
    });

    // 监听标签页从窗口分离
    chrome.tabs.onDetached.addListener(async (tabId, detachInfo) => {
      try {
        console.log('Tab detached:', tabId, detachInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_detached');
      } catch (error) {
        console.error('Error handling tab detach:', error);
      }
    });
  }

  /**
   * 设置存储监听器
   */
  private setupStorageListeners(): void {
    StorageManager.onChanged((changes) => {
      console.log('Storage changed:', changes);
      
      // 通知侧边栏更新
      this.notifySidePanelUpdate(changes);
    });
  }

  /**
   * 初始化默认数据
   */
  private async initializeDefaultData(): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data!.length === 0) {
        console.log('No workspaces found, creating default workspace templates');
        
        // 可以选择性地创建一些默认工作区
        // 这里暂时不自动创建，让用户自己选择
      }
    } catch (error) {
      console.error('Error initializing default data:', error);
    }
  }

  /**
   * 根据索引切换工作区
   */
  private async switchToWorkspaceByIndex(index: number): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('Failed to get workspaces:', workspacesResult.error);
        return;
      }

      const workspaces = workspacesResult.data!;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        const result = await WorkspaceSwitcher.switchToWorkspace(workspace.id);
        if (result.success) {
          console.log(`Switched to workspace: ${workspace.name}`);
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        } else {
          console.error('Failed to switch workspace:', result.error);
        }
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error('Error switching workspace by index:', error);
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  private async toggleSidePanel(): Promise<void> {
    try {
      // 获取当前活跃的标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id!;
        // 这里可以实现侧边栏的切换逻辑
        // 由于Chrome API限制，我们主要依赖用户点击扩展图标
        console.log('Toggle side panel for tab:', tabId);
      }
    } catch (error) {
      console.error('Error toggling side panel:', error);
    }
  }

  /**
   * 显示通知
   */
  private showNotification(message: string, icon?: string): void {
    try {
      // 创建简单的通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'WorkSpace Pro',
        message: `${icon || '🚀'} ${message}`,
      });
      console.log('Notification shown:', message);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  /**
   * 通知侧边栏更新
   */
  private notifySidePanelUpdate(_changes: { [key: string]: chrome.storage.StorageChange }): void {
    try {
      // 这里可以通过消息传递通知侧边栏更新
      // 由于侧边栏是独立的页面，我们主要依赖存储监听
      console.log('Notifying side panel of storage changes');
    } catch (error) {
      console.error('Error notifying side panel:', error);
    }
  }



  /**
   * 通知全局用户标签页状态变化
   */
  private async notifyGlobalUserTabsStateChange(eventType: string): Promise<void> {
    try {
      // 延迟一小段时间，确保标签页操作完成
      setTimeout(async () => {
        try {
          // 在background script中，只发送Chrome扩展消息，不使用window对象
          if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({
              type: 'USER_TABS_VISIBILITY_CHANGED',
              workspaceId: 'global',
              eventType: eventType
            }).catch(error => {
              console.log('发送用户标签页状态变化消息失败:', error);
            });
          }

          console.log(`📢 已通知全局用户标签页状态变化: ${eventType}`);
        } catch (error) {
          console.error('发送全局用户标签页状态变化通知失败:', error);
        }
      }, 50); // 延迟50ms确保操作完成
    } catch (error) {
      console.error('通知全局用户标签页状态变化失败:', error);
    }
  }
}

// 初始化后台服务
new BackgroundService();
