import React, { useState, useEffect } from 'react';
import { ExternalLink, X, Pin, PinOff, Edit, Check, Trash2, MoreHorizontal } from 'lucide-react';
import { Website } from '@/types/workspace';

interface WebsiteListProps {
  websites: Website[];
  onRemoveWebsite: (websiteId: string) => void;
  onEditWebsite: (website: Website) => void;
  onReorderWebsites?: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
  batchMode?: boolean;
  onExitBatchMode?: () => void;
}

/**
 * 网站列表组件
 */
const WebsiteList: React.FC<WebsiteListProps> = ({
  websites,
  onRemoveWebsite,
  onEditWebsite,
  onReorderWebsites: _onReorderWebsites,
  onTogglePin,
  onBatchPin,
  onBatchDelete,
  batchMode = false,
  onExitBatchMode,
}) => {
  const [selectedWebsites, setSelectedWebsites] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(batchMode);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState<string | null>(null);
  const [editingUrl, setEditingUrl] = useState('');
  const [editingTitle, setEditingTitle] = useState('');



  // 同步批量模式状态
  useEffect(() => {
    setIsSelectionMode(batchMode);
    if (!batchMode) {
      setSelectedWebsites(new Set());
    }
  }, [batchMode]);

  // 点击外部关闭更多菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showMoreMenu) {
        setShowMoreMenu(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showMoreMenu]);

  /**
   * 智能重复检测和固定逻辑（升级版：六层智能匹配策略）
   */
  const findAndHandleExistingTab = async (website: Website): Promise<{
    found: boolean;
    tabId?: number;
    confidence?: number;
    strategy?: string;
    reason?: string;
  }> => {
    try {
      console.log('🔍 智能检查重复标签页:', website.url);

      // 使用新的智能匹配系统
      const matchResult = await TabManager.findMatchingTabsIntelligent(website.url, {
        currentWindowOnly: true,
        minConfidence: 0.8, // 对于重复检测，要求高置信度
        includeDiscarded: false
      });

      if (!matchResult.success) {
        console.error('❌ 智能匹配查询失败:', matchResult.error);
        return { found: false };
      }

      const { tabs, bestMatch, matchDetails } = matchResult.data!;

      if (tabs.length === 0) {
        console.log('❌ 未找到重复标签页');
        return { found: false };
      }

      // 获取最佳匹配的详细信息
      const bestMatchDetail = matchDetails[0];
      const existingTab = bestMatch || tabs[0];

      console.log(`✅ 找到重复标签页: ${existingTab.title}`);
      console.log(`   匹配策略: ${bestMatchDetail?.strategy || 'unknown'}`);
      console.log(`   置信度: ${((bestMatchDetail?.confidence || 0) * 100).toFixed(1)}%`);
      console.log(`   原因: ${bestMatchDetail?.reason || 'N/A'}`);
      console.log(`   固定状态: ${existingTab.isPinned ? '已固定' : '未固定'}`);

      // 检查固定状态
      if (website.isPinned && !existingTab.isPinned) {
        // 需要固定但当前未固定，设置为固定
        await chrome.tabs.update(existingTab.id, { pinned: true });
        console.log('📌 将现有标签页设置为固定');
      }

      // 激活标签页
      await chrome.tabs.update(existingTab.id, { active: true });
      console.log('🎯 激活现有标签页');

      return {
        found: true,
        tabId: existingTab.id,
        confidence: bestMatchDetail?.confidence,
        strategy: bestMatchDetail?.strategy,
        reason: bestMatchDetail?.reason
      };
    } catch (error) {
      console.error('❌ 智能重复检测失败:', error);
      return { found: false };
    }
  };

  /**
   * 处理网站点击
   */
  const handleWebsiteClick = async (website: Website) => {
    try {
      console.log('🔍 点击网站:', website.title, website.url);

      // 智能重复检测
      const existingResult = await findAndHandleExistingTab(website);

      if (existingResult.found) {
        // 找到现有标签页，显示匹配信息
        if (existingResult.confidence && existingResult.strategy) {
          const confidencePercent = (existingResult.confidence * 100).toFixed(1);
          console.log(`🎯 激活现有标签页 (${existingResult.strategy}匹配, ${confidencePercent}%置信度)`);

          // 可以在这里添加用户通知，显示匹配策略
          // 例如：显示一个简短的提示，说明为什么这两个标签页被认为是重复的
          if (existingResult.confidence < 0.9) {
            console.log(`ℹ️ 匹配原因: ${existingResult.reason || '智能匹配'}`);
          }
        }
      } else {
        // 没有找到现有标签页，创建新的
        console.log('🆕 创建新标签页');
        await chrome.tabs.create({
          url: website.url,
          pinned: website.isPinned,
          active: true
        });
        console.log('✅ 新标签页创建完成');
      }
    } catch (error) {
      console.error('Failed to open website:', error);
    }
  };

  /**
   * 处理固定状态切换
   */
  const handleTogglePin = async (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();

    try {
      // 查找对应的标签页
      const tabs = await chrome.tabs.query({ url: website.url + '*' });

      if (tabs.length > 0) {
        // 切换标签页的固定状态
        const newPinnedState = !tabs[0].pinned;
        await chrome.tabs.update(tabs[0].id!, { pinned: newPinnedState });

        // 通知父组件更新
        if (onTogglePin) {
          onTogglePin(website.id, newPinnedState);
        }

        console.log(`✅ 切换标签页固定状态: ${website.title} -> ${newPinnedState ? '固定' : '取消固定'}`);
      } else {
        console.log(`ℹ️ 标签页未打开，无法切换固定状态: ${website.title}`);
      }
    } catch (error) {
      console.error('Failed to toggle pin state:', error);
    }
  };

  /**
   * 处理移除网站
   */
  const handleRemoveWebsite = (e: React.MouseEvent, websiteId: string) => {
    e.stopPropagation();
    onRemoveWebsite(websiteId);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    onEditWebsite(website);
  };

  /**
   * 处理选择模式切换
   */
  const handleToggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedWebsites(new Set());
  };

  /**
   * 处理网站选择
   */
  const handleWebsiteSelect = (websiteId: string) => {
    const newSelected = new Set(selectedWebsites);
    if (newSelected.has(websiteId)) {
      newSelected.delete(websiteId);
    } else {
      newSelected.add(websiteId);
    }
    setSelectedWebsites(newSelected);
  };

  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = () => {
    if (selectedWebsites.size === websites.length) {
      setSelectedWebsites(new Set());
    } else {
      setSelectedWebsites(new Set(websites.map(w => w.id)));
    }
  };

  /**
   * 处理批量固定
   */
  const handleBatchPin = async (isPinned: boolean) => {
    const selectedIds = Array.from(selectedWebsites);

    try {
      // 更新所有选中网站对应的标签页
      for (const websiteId of selectedIds) {
        const website = websites.find(w => w.id === websiteId);
        if (website) {
          const tabs = await chrome.tabs.query({ url: website.url + '*' });
          if (tabs.length > 0) {
            await chrome.tabs.update(tabs[0].id!, { pinned: isPinned });
          }
        }
      }

      // 批量操作完成，无需更新本地状态

      // 通知父组件
      if (onBatchPin) {
        onBatchPin(selectedIds, isPinned);
      }

      // 退出选择模式
      setIsSelectionMode(false);
      setSelectedWebsites(new Set());
    } catch (error) {
      console.error('Failed to batch pin websites:', error);
    }
  };

  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchDelete) {
      onBatchDelete(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    setSelectedWebsites(new Set());
  };

  /**
   * 获取网站图标
   */
  const getWebsiteIcon = (website: Website) => {
    if (website.favicon && website.favicon !== '') {
      return (
        <img
          src={website.favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            // 如果图标加载失败，显示默认图标
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }
    
    // 默认图标
    return (
      <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
        <ExternalLink className="w-2.5 h-2.5 text-slate-400" />
      </div>
    );
  };

  /**
   * 格式化URL显示
   */
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  // 按order字段排序
  const sortedWebsites = [...websites].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-1">
      {/* 批量操作工具栏 - 只在批量模式下显示 */}
      {websites.length > 0 && batchMode && isSelectionMode && (
        <div className="mb-2 relative">
          <div className="flex items-center justify-between px-1 py-1.5 bg-slate-700/80 rounded border border-slate-600/50">
            <div className="flex items-center gap-2">
              {isSelectionMode && (
                <>
                  <button
                    onClick={handleSelectAll}
                    className="px-2 py-1 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded text-xs transition-all duration-200"
                  >
                    {selectedWebsites.size === websites.length ? '取消' : '全选'}
                  </button>

                  <div className="flex items-center gap-1 px-1.5 py-1 bg-slate-800/50 rounded">
                    <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                    <span className="text-xs text-slate-300">
                      {selectedWebsites.size}
                    </span>
                  </div>
                </>
              )}
            </div>

            {/* 右侧按钮组 */}
            <div className="flex items-center gap-1">
              {/* 批量操作按钮 */}
              {selectedWebsites.size > 0 && (
                <>
                  <button
                    onClick={() => handleBatchPin(true)}
                    className="flex items-center justify-center w-6 h-6 bg-blue-600 text-white hover:bg-blue-700 rounded transition-all duration-200"
                    title="全部固定"
                  >
                    <Pin className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => handleBatchPin(false)}
                    className="flex items-center justify-center w-6 h-6 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded transition-all duration-200"
                    title="取消固定"
                  >
                    <PinOff className="w-3 h-3" />
                  </button>
                  <button
                    onClick={handleBatchDelete}
                    className="flex items-center justify-center w-6 h-6 bg-red-600 text-white hover:bg-red-700 rounded transition-all duration-200"
                    title="删除选中"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </>
              )}

              {/* 退出批量模式按钮 */}
              <button
                onClick={onExitBatchMode}
                className="flex items-center justify-center w-6 h-6 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded transition-all duration-200"
                title="退出批量模式"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      )}

      {sortedWebsites.map((website) => {
        const isSelected = selectedWebsites.has(website.id);

        return (
        <div
          key={website.id}
          className={`website-item ${isSelected ? 'bg-blue-600/20 border-blue-500' : ''}`}
          onClick={() => isSelectionMode ? handleWebsiteSelect(website.id) : handleWebsiteClick(website)}
        >
          {/* 选择框 */}
          {isSelectionMode && (
            <div className="flex-shrink-0">
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                isSelected
                  ? 'bg-blue-600 border-blue-600'
                  : 'border-slate-400'
              }`}>
                {isSelected && <Check className="w-3 h-3 text-white" />}
              </div>
            </div>
          )}

          {/* 网站图标 */}
          <div className="flex-shrink-0">
            {getWebsiteIcon(website)}
          </div>

          {/* 网站信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-sm text-white truncate">
                {website.title}
              </span>
            </div>
            <p className="text-xs text-slate-400 truncate">
              {formatUrl(website.url)}
            </p>
          </div>

          {/* 操作按钮 */}
          {!isSelectionMode && (
            <div className="website-actions flex items-center gap-1">
              {/* 固定状态切换按钮 */}
              <button
                onClick={(e) => handleTogglePin(e, website)}
                className="p-1 rounded transition-colors duration-150 hover:bg-slate-500"
                title={website.isPinned ? '取消固定标签页' : '固定标签页'}
              >
                {website.isPinned ? (
                  <PinOff className="w-3 h-3 text-blue-400" />
                ) : (
                  <Pin className="w-3 h-3 text-slate-400" />
                )}
              </button>

              <button
                onClick={async (e) => {
                  e.stopPropagation();

                  try {
                    console.log('🔍 在新标签页中打开:', website.url);

                    // 创建一个临时网站对象，强制固定状态为true
                    const websiteWithPin = { ...website, isPinned: true };

                    // 使用智能重复检测
                    const existingResult = await findAndHandleExistingTab(websiteWithPin);

                    if (!existingResult.found) {
                      // 没有找到现有标签页，创建新的固定标签页
                      await chrome.tabs.create({
                        url: website.url,
                        pinned: true,  // 强制固定
                        active: true
                      });
                      console.log('✅ 在新标签页中打开并固定:', website.url);
                    }
                  } catch (error) {
                    console.error('在新标签页中打开失败:', error);
                    // 降级到window.open
                    window.open(website.url, '_blank');
                  }
                }}
                className="p-1 hover:bg-slate-500 rounded transition-colors duration-150"
                title="在新标签页中打开"
              >
                <ExternalLink className="w-3 h-3 text-slate-400" />
              </button>
              <button
                onClick={(e) => handleEditWebsite(e, website)}
                className="p-1 hover:bg-blue-600 rounded transition-colors duration-150"
                title="编辑网站"
              >
                <Edit className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
              <button
                onClick={(e) => handleRemoveWebsite(e, website.id)}
                className="p-1 hover:bg-red-600 rounded transition-colors duration-150"
                title="移除网站"
              >
                <X className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
            </div>
          )}
        </div>
        );
      })}
    </div>
  );
};

export default WebsiteList;
